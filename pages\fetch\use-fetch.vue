<script setup lang="ts">
definePageMeta({
  layout: 'empty',
})

useHead({
  title: 'useFetch 详细指南 - Nuxt 3',
  meta: [
    { name: 'description', content: 'Nuxt 3 useFetch 的完整学习指南和最佳实践' }
  ]
})

// ==================== 基础示例 ====================

// 1. 基础用法
const basicData = ref({ message: 'Hello from useFetch!', timestamp: Date.now() })
const basicPending = ref(false)
const basicError = ref(null)

// 2. 带参数的请求
const userId = ref('1')
const userData = ref({ id: 1, name: '张三', email: '<EMAIL>' })
const userPending = ref(false)

const refreshUser = () => {
  userPending.value = true
  setTimeout(() => {
    userData.value = {
      id: parseInt(userId.value),
      name: `用户${userId.value}`,
      email: `user${userId.value}@example.com`
    }
    userPending.value = false
  }, 500)
}

// 3. 响应式参数
const searchQuery = ref('')
const searchResults = ref(null)
const searchPending = ref(false)

watch(searchQuery, (newQuery) => {
  if (newQuery.trim()) {
    searchPending.value = true
    setTimeout(() => {
      searchResults.value = {
        query: newQuery,
        results: [`结果1: ${newQuery}`, `结果2: ${newQuery}`, `结果3: ${newQuery}`],
        total: 3
      }
      searchPending.value = false
    }, 300)
  } else {
    searchResults.value = null
  }
})

// 4. POST 请求
const postData = ref(null)
const postPending = ref(false)

const executePost = async () => {
  postPending.value = true
  setTimeout(() => {
    postData.value = { 
      success: true, 
      id: Date.now(),
      message: 'POST 请求成功！'
    }
    postPending.value = false
  }, 1000)
}

// 5. 表单提交
const formData = reactive({
  name: '',
  email: '',
  message: ''
})

const submitting = ref(false)

const submitForm = async () => {
  submitting.value = true
  setTimeout(() => {
    console.log('表单提交:', formData)
    // 模拟成功响应
    alert('表单提交成功！')
    Object.assign(formData, { name: '', email: '', message: '' })
    submitting.value = false
  }, 1000)
}

// 6. 分页数据
const currentPage = ref(1)
const pageSize = ref(5)
const paginatedData = ref({
  items: ['文章1', '文章2', '文章3', '文章4', '文章5'],
  total: 25,
  hasMore: true,
  currentPage: 1
})
const pageLoading = ref(false)

// 7. 条件请求
const shouldFetch = ref(false)
const conditionalData = ref(null)

watch(shouldFetch, (newValue) => {
  if (newValue) {
    conditionalData.value = { 
      message: '条件获取的数据', 
      enabled: true,
      timestamp: new Date().toLocaleTimeString()
    }
  } else {
    conditionalData.value = null
  }
})

// ==================== 工具方法 ====================

const changeUser = (id) => {
  userId.value = id.toString()
  refreshUser()
}

const nextPage = () => {
  if (paginatedData.value?.hasMore) {
    pageLoading.value = true
    currentPage.value++
    setTimeout(() => {
      const startIndex = (currentPage.value - 1) * pageSize.value
      paginatedData.value = {
        items: Array.from({ length: pageSize.value }, (_, i) => `文章${startIndex + i + 1}`),
        total: 25,
        hasMore: currentPage.value < 5,
        currentPage: currentPage.value
      }
      pageLoading.value = false
    }, 500)
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    pageLoading.value = true
    currentPage.value--
    setTimeout(() => {
      const startIndex = (currentPage.value - 1) * pageSize.value
      paginatedData.value = {
        items: Array.from({ length: pageSize.value }, (_, i) => `文章${startIndex + i + 1}`),
        total: 25,
        hasMore: currentPage.value < 5,
        currentPage: currentPage.value
      }
      pageLoading.value = false
    }, 500)
  }
}

const toggleShouldFetch = () => {
  shouldFetch.value = !shouldFetch.value
}

const handleSubmit = async () => {
  if (formData.name && formData.email && formData.message) {
    await submitForm()
  }
}

// ==================== 代码示例 ====================

const codeExamples = {
  basic: `// 基础用法
const { data, pending, error } = await useFetch('/api/users')

// 检查状态
if (pending.value) {
  console.log('加载中...')
}

if (error.value) {
  console.error('请求失败:', error.value)
}

console.log('数据:', data.value)`,

  params: `// 带参数的请求
const userId = ref(1)
const { data: user, refresh } = await useFetch(\`/api/users/\${userId.value}\`)

// 刷新数据
await refresh()`,

  reactive: `// 响应式参数
const searchQuery = ref('')
const { data: results } = await useFetch('/api/search', {
  query: {
    q: searchQuery
  },
  watch: [searchQuery] // 监听参数变化
})`,

  post: `// POST 请求
const { data, execute } = await useFetch('/api/posts', {
  method: 'POST',
  body: {
    title: 'New Post',
    content: 'Post content'
  },
  immediate: false // 不立即执行
})

// 手动执行
await execute()`,

  advanced: `// 高级配置
const { data } = await useFetch('/api/data', {
  key: 'unique-key',           // 缓存键
  server: true,                // 服务端执行
  default: () => [],           // 默认值
  transform: (data) => {       // 数据转换
    return data.map(item => ({
      ...item,
      formatted: true
    }))
  },
  onResponse({ response }) {   // 响应处理
    console.log('Response:', response.status)
  },
  onResponseError({ error }) { // 错误处理
    console.error('Error:', error)
  }
})`
}
</script>

<template>
  <div class="use-fetch-guide">
    <header class="guide-header">
      <h1>🎯 useFetch 详细指南</h1>
      <p class="subtitle">掌握 Nuxt 3 中最重要的数据获取方法</p>
    </header>

    <!-- 基础概念 -->
    <section class="guide-section">
      <h2>📚 基础概念</h2>
      <div class="concept-grid">
        <div class="concept-card">
          <h3>什么是 useFetch？</h3>
          <p>useFetch 是 Nuxt 3 提供的通用数据获取组合式函数，它结合了 SSR 支持、响应式更新和智能缓存。</p>
        </div>
        <div class="concept-card">
          <h3>核心特性</h3>
          <ul>
            <li>✅ 自动 SSR 支持</li>
            <li>✅ 响应式参数</li>
            <li>✅ 智能缓存</li>
            <li>✅ 错误处理</li>
            <li>✅ TypeScript 支持</li>
          </ul>
        </div>
        <div class="concept-card">
          <h3>返回值</h3>
          <ul>
            <li><code>data</code> - 响应数据</li>
            <li><code>pending</code> - 加载状态</li>
            <li><code>error</code> - 错误信息</li>
            <li><code>refresh</code> - 刷新方法</li>
            <li><code>execute</code> - 执行方法</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 基础用法 -->
    <section class="guide-section">
      <h2>🚀 基础用法</h2>
      
      <div class="example-grid">
        <div class="example-card">
          <h3>1. 基础请求</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples.basic }}</code></pre>
          </div>
          <div class="demo-area">
            <div class="result">
              <p><strong>状态:</strong> {{ basicPending ? '加载中...' : '完成' }}</p>
              <p><strong>数据:</strong> {{ JSON.stringify(basicData) }}</p>
            </div>
          </div>
        </div>

        <div class="example-card">
          <h3>2. 带参数请求</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples.params }}</code></pre>
          </div>
          <div class="demo-area">
            <div class="controls">
              <button @click="changeUser(1)">用户 1</button>
              <button @click="changeUser(2)">用户 2</button>
              <button @click="changeUser(3)">用户 3</button>
              <button @click="refreshUser()">刷新</button>
            </div>
            <div class="result">
              <p><strong>状态:</strong> {{ userPending ? '加载中...' : '完成' }}</p>
              <p><strong>用户:</strong> {{ JSON.stringify(userData) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 响应式参数 -->
    <section class="guide-section">
      <h2>🔄 响应式参数</h2>
      
      <div class="example-card">
        <h3>3. 搜索示例</h3>
        <div class="code-block">
          <pre><code>{{ codeExamples.reactive }}</code></pre>
        </div>
        <div class="demo-area">
          <div class="controls">
            <input 
              v-model="searchQuery" 
              placeholder="输入搜索关键词..."
              class="search-input"
            >
          </div>
          <div class="result">
            <p><strong>搜索词:</strong> {{ searchQuery || '无' }}</p>
            <p><strong>状态:</strong> {{ searchPending ? '搜索中...' : '完成' }}</p>
            <p><strong>结果:</strong> {{ searchResults?.results?.join(', ') || '暂无结果' }}</p>
            <p v-if="searchResults"><strong>总数:</strong> {{ searchResults.total }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- POST 请求 -->
    <section class="guide-section">
      <h2>📝 POST 请求</h2>
      
      <div class="example-grid">
        <div class="example-card">
          <h3>4. POST 请求示例</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples.post }}</code></pre>
          </div>
          <div class="demo-area">
            <div class="controls">
              <button @click="executePost" :disabled="postPending">
                {{ postPending ? '提交中...' : '执行 POST 请求' }}
              </button>
            </div>
            <div class="result">
              <p><strong>状态:</strong> {{ postPending ? '提交中...' : '完成' }}</p>
              <p><strong>响应:</strong> {{ postData ? JSON.stringify(postData) : '暂无数据' }}</p>
            </div>
          </div>
        </div>

        <div class="example-card">
          <h3>5. 表单提交</h3>
          <div class="demo-area">
            <form @submit.prevent="handleSubmit" class="demo-form">
              <div class="form-group">
                <label>姓名:</label>
                <input v-model="formData.name" type="text" required>
              </div>
              <div class="form-group">
                <label>邮箱:</label>
                <input v-model="formData.email" type="email" required>
              </div>
              <div class="form-group">
                <label>消息:</label>
                <textarea v-model="formData.message" required></textarea>
              </div>
              <button type="submit" :disabled="submitting">
                {{ submitting ? '提交中...' : '提交表单' }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- 分页数据 -->
    <section class="guide-section">
      <h2>📄 分页数据</h2>
      
      <div class="example-card">
        <h3>6. 分页列表</h3>
        <div class="demo-area">
          <div class="pagination-controls">
            <button @click="prevPage" :disabled="currentPage <= 1">上一页</button>
            <span>第 {{ currentPage }} 页 / 共 5 页</span>
            <button @click="nextPage" :disabled="!paginatedData?.hasMore">下一页</button>
          </div>
          <div class="result">
            <p><strong>状态:</strong> {{ pageLoading ? '加载中...' : '完成' }}</p>
            <p><strong>总数:</strong> {{ paginatedData?.total || 0 }}</p>
            <p><strong>当前页:</strong> {{ paginatedData?.items?.join(', ') || '无' }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 条件请求 -->
    <section class="guide-section">
      <h2>🔀 条件请求</h2>
      
      <div class="example-card">
        <h3>7. 条件加载</h3>
        <div class="demo-area">
          <div class="controls">
            <button @click="toggleShouldFetch">
              {{ shouldFetch ? '停止获取' : '开始获取' }}
            </button>
          </div>
          <div class="result">
            <p><strong>获取状态:</strong> {{ shouldFetch ? '启用' : '禁用' }}</p>
            <p><strong>数据:</strong> {{ conditionalData ? JSON.stringify(conditionalData) : '暂无数据' }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 高级配置 -->
    <section class="guide-section">
      <h2>⚙️ 高级配置</h2>
      
      <div class="example-card">
        <h3>8. 完整配置示例</h3>
        <div class="code-block">
          <pre><code>{{ codeExamples.advanced }}</code></pre>
        </div>
        <div class="config-table">
          <table>
            <thead>
              <tr>
                <th>配置项</th>
                <th>类型</th>
                <th>说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>key</code></td>
                <td>string</td>
                <td>缓存键，用于去重和缓存</td>
              </tr>
              <tr>
                <td><code>method</code></td>
                <td>string</td>
                <td>HTTP 方法 (GET, POST, PUT, DELETE)</td>
              </tr>
              <tr>
                <td><code>query</code></td>
                <td>object</td>
                <td>URL 查询参数</td>
              </tr>
              <tr>
                <td><code>body</code></td>
                <td>any</td>
                <td>请求体数据</td>
              </tr>
              <tr>
                <td><code>headers</code></td>
                <td>object</td>
                <td>请求头</td>
              </tr>
              <tr>
                <td><code>server</code></td>
                <td>boolean</td>
                <td>是否在服务端执行</td>
              </tr>
              <tr>
                <td><code>lazy</code></td>
                <td>boolean</td>
                <td>是否懒加载</td>
              </tr>
              <tr>
                <td><code>immediate</code></td>
                <td>boolean</td>
                <td>是否立即执行</td>
              </tr>
              <tr>
                <td><code>watch</code></td>
                <td>array</td>
                <td>监听的响应式变量</td>
              </tr>
              <tr>
                <td><code>transform</code></td>
                <td>function</td>
                <td>数据转换函数</td>
              </tr>
              <tr>
                <td><code>default</code></td>
                <td>function</td>
                <td>默认值函数</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <!-- 导航 -->
    <div class="navigation">
      <NuxtLink to="/fetch">← 返回 Fetch 中心</NuxtLink>
      <NuxtLink to="/fetch/use-lazy-fetch">useLazyFetch →</NuxtLink>
    </div>
  </div>
</template>

<style lang="less" scoped>
.use-fetch-guide {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.guide-header {
  text-align: center;
  margin-bottom: 50px;
  padding: 40px 0;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  border-radius: 12px;

  h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
  }

  .subtitle {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
  }
}

.guide-section {
  margin-bottom: 60px;

  h2 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
    font-size: 1.8em;
  }
}

.concept-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.concept-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);

  h3 {
    color: #2c3e50;
    margin-top: 0;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin: 8px 0;
      padding-left: 5px;

      code {
        background: #f8f9fa;
        color: #e74c3c;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.9em;
      }
    }
  }
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
}

.example-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);

  h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
  }
}

.code-block {
  background: #2c3e50;
  border-radius: 8px;
  margin: 15px 0;
  overflow-x: auto;

  pre {
    margin: 0;
    padding: 20px;
    color: #ecf0f1;

    code {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

.demo-area {
  margin-top: 20px;
}

.controls {
  margin: 15px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;

  button {
    padding: 8px 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;

    &:hover:not(:disabled) {
      background: #2980b9;
      transform: translateY(-1px);
    }

    &:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
    }
  }
}

.search-input {
  width: 100%;
  padding: 10px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #3498db;
  }
}

.result {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-top: 15px;

  p {
    margin: 8px 0;
    font-size: 14px;

    strong {
      color: #2c3e50;
    }
  }
}

.demo-form {
  .form-group {
    margin: 15px 0;

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #2c3e50;
    }

    input, textarea {
      width: 100%;
      padding: 10px;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: #3498db;
      }
    }

    textarea {
      height: 80px;
      resize: vertical;
    }
  }

  button[type="submit"] {
    width: 100%;
    padding: 12px;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;

    &:hover:not(:disabled) {
      background: #229954;
    }

    &:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
    }
  }
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 15px 0;

  span {
    font-weight: 500;
    color: #2c3e50;
  }
}

.config-table {
  margin-top: 20px;
  overflow-x: auto;

  table {
    width: 100%;
    border-collapse: collapse;
    background: white;

    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e1e5e9;
    }

    th {
      background: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
    }

    code {
      background: #f8f9fa;
      color: #e74c3c;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.9em;
    }
  }
}

.navigation {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin: 50px 0;
  flex-wrap: wrap;

  a {
    padding: 12px 24px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;

    &:hover {
      background: #2980b9;
      transform: translateY(-2px);
    }
  }
}
</style>

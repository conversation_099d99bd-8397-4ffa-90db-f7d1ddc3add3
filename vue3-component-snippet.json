{"Vue 3 Component with Script Setup": {"prefix": "vue3", "body": ["<script setup lang=\"ts\">", "$1", "</script>", "", "<template>", "  <div>", "    $2", "  </div>", "</template>", "", "<style lang=\"less\" scoped>", "$3", "</style>"], "description": "Vue 3 component with script setup TypeScript, template, and scoped Less styles"}, "Vue 3 Component with Props": {"prefix": "vue3props", "body": ["<script setup lang=\"ts\">", "interface Props {", "  $1", "}", "", "const props = defineProps<Props>()", "$2", "</script>", "", "<template>", "  <div>", "    $3", "  </div>", "</template>", "", "<style lang=\"less\" scoped>", "$4", "</style>"], "description": "Vue 3 component with TypeScript props interface"}, "Vue 3 Component with Emits": {"prefix": "vue3emits", "body": ["<script setup lang=\"ts\">", "interface Props {", "  $1", "}", "", "interface Emits {", "  $2", "}", "", "const props = defineProps<Props>()", "const emit = defineEmits<Emits>()", "$3", "</script>", "", "<template>", "  <div>", "    $4", "  </div>", "</template>", "", "<style lang=\"less\" scoped>", "$5", "</style>"], "description": "Vue 3 component with TypeScript props and emits interfaces"}, "Vue 3 Component Full": {"prefix": "vue3full", "body": ["<script setup lang=\"ts\">", "import { ref, reactive, computed, onMounted } from 'vue'", "", "interface Props {", "  $1", "}", "", "interface Emits {", "  $2", "}", "", "const props = defineProps<Props>()", "const emit = defineEmits<Emits>()", "", "// 响应式数据", "const $3 = ref('')", "", "// 计算属性", "const $4 = computed(() => {", "  return $5", "})", "", "// 方法", "const $6 = () => {", "  $7", "}", "", "// 生命周期", "onMounted(() => {", "  $8", "})", "</script>", "", "<template>", "  <div class=\"$9\">", "    $10", "  </div>", "</template>", "", "<style lang=\"less\" scoped>", ".$9 {", "  $11", "}", "</style>"], "description": "Complete Vue 3 component with common features"}}
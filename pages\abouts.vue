<script setup lang="ts">
definePageMeta({
	title: '某个页面',
})
useHead({
	title: 'about page',
})

const result = ref()
const age = ref()

let refreshFn:()=>unknown

const onFetch = async () => {
	if (refreshFn) {
    
    console.log('%c [ refreshFn ]-16', 'font-size:13px; background:#7c8753; color:#c0cb97;', );
		refreshFn?.()
		return
	}


	const { data, status } = await useFetch('/api/fetch', {
		// pick: ['age'],
		query: {
			name: 'Rex2321123',
      sex: 'male',
      age: age.value , // 动态参数，需要响应式，需要使用 ref 包裹

		},
	})


result.value = data.value	
}


const onClear = () => { 
  clearNuxtData();
  clearNuxtState();
}

// onBeforeMount(async() => {
// 	await onFetch()
// })
</script>

<template>
	<section>
		{{ result }}
		<hr >
		<p>此页面将在 /about 路由下显示。</p>
		<img src="/favicon.ico" alt="" srcset="" >
    <el-input v-model="age"/>
		<div>
			<nuxt-link to="/"> 回到首页 </nuxt-link>
			<button @click=" onFetch">fetch</button>
			<button @click="onClear">clear</button>
		</div>
	</section>
</template>

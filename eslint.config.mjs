// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs'

export default withNuxt(
  // Your custom configs here
  {
    rules: {
      // 禁用 Vue 2 的单根元素规则，因为 Vue 3 支持多个根元素
      'vue/no-multiple-template-root': 'off',

      // 其他可能需要禁用的 Vue 2 规则
      'vue/no-v-for-template-key': 'off',
      'vue/no-v-model-argument': 'off',

      // 可选：如果你想要更宽松的规则
      // 'vue/multi-word-component-names': 'off',
      // 'vue/require-default-prop': 'off',
    }
  }
)

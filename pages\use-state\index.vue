<script setup lang="ts">
// 设置页面元数据
useHead({
  title: 'useState 学习中心 - Nuxt 3',
  meta: [
    { name: 'description', content: 'Nuxt 3 useState 的完整学习资源和示例' }
  ]
})

// 示例列表
const examples = [
  {
    title: '📚 详细文档',
    description: '完整的 useState 使用文档和最佳实践',
    path: '/use-state/docs',
    icon: '📖',
    color: '#3498db'
  },
  {
    title: '📝 基础示例',
    description: '学习 useState 的基础用法和常见场景',
    path: '/use-state/examples',
    icon: '🎯',
    color: '#27ae60'
  },
  {
    title: '🚀 高级示例',
    description: '探索 useState 的高级用法和复杂状态管理',
    path: '/use-state/advanced',
    icon: '⚡',
    color: '#e74c3c'
  },
  {
    title: '🔍 对比分析',
    description: 'useState 与 ref/reactive 的详细对比',
    path: '/use-state/differences',
    icon: '⚖️',
    color: '#9b59b6'
  },
  {
    title: '🎮 交互对比',
    description: '通过交互式示例理解两者区别',
    path: '/use-state/comparison',
    icon: '🎯',
    color: '#f39c12'
  },
  {
    title: '👤 用户详情页',
    description: '动态路由中的 useState 使用示例',
    path: '/use-state/1',
    icon: '🔗',
    color: '#34495e'
  }
]

// 核心概念
const concepts = [
  {
    title: 'SSR 友好',
    description: '自动处理服务端渲染和客户端水合之间的状态同步',
    icon: '🔄'
  },
  {
    title: '全局共享',
    description: '相同 key 的状态在整个应用中共享',
    icon: '🌐'
  },
  {
    title: '响应式',
    description: '基于 Vue 3 的响应式系统',
    icon: '⚡'
  },
  {
    title: '类型安全',
    description: '完整的 TypeScript 支持',
    icon: '🛡️'
  }
]

// 快速开始代码示例
const quickStartCode = `// 基础用法
const counter = useState<number>('counter', () => 0)

// 复杂状态
interface User {
  id: number
  name: string
  email: string
}

const user = useState<User>('user', () => ({
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>'
}))

// 操作状态
const increment = () => counter.value++
const updateUser = (newUser: User) => user.value = newUser`
</script>

<template>
  <div class="learning-center">
    <!-- 头部 -->
    <header class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="emoji">🎓</span>
          useState 学习中心
        </h1>
        <p class="hero-subtitle">
          掌握 Nuxt 3 中最重要的状态管理工具
        </p>
        <div class="hero-stats">
          <div class="stat">
            <span class="stat-number">6</span>
            <span class="stat-label">个示例</span>
          </div>
          <div class="stat">
            <span class="stat-number">500+</span>
            <span class="stat-label">行代码</span>
          </div>
          <div class="stat">
            <span class="stat-number">∞</span>
            <span class="stat-label">种可能</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 核心概念 -->
    <section class="concepts-section">
      <h2 class="section-title">🎯 核心概念</h2>
      <div class="concepts-grid">
        <div v-for="concept in concepts" :key="concept.title" class="concept-card">
          <div class="concept-icon">{{ concept.icon }}</div>
          <h3>{{ concept.title }}</h3>
          <p>{{ concept.description }}</p>
        </div>
      </div>
    </section>

    <!-- 快速开始 -->
    <section class="quick-start-section">
      <h2 class="section-title">⚡ 快速开始</h2>
      <div class="quick-start-content">
        <div class="quick-start-text">
          <h3>立即开始使用 useState</h3>
          <p>useState 是 Nuxt 3 中用于管理响应式状态的核心工具。它提供了一种简单而强大的方式来在组件之间共享状态，同时保持 SSR 兼容性。</p>
          <ul>
            <li>✅ 零配置，开箱即用</li>
            <li>✅ 完整的 TypeScript 支持</li>
            <li>✅ 自动的 SSR/客户端同步</li>
            <li>✅ 基于 Vue 3 响应式系统</li>
          </ul>
        </div>
        <div class="quick-start-code">
          <pre><code>{{ quickStartCode }}</code></pre>
        </div>
      </div>
    </section>

    <!-- 示例列表 -->
    <section class="examples-section">
      <h2 class="section-title">📚 学习资源</h2>
      <div class="examples-grid">
        <NuxtLink
          v-for="example in examples"
          :key="example.title"
          :to="example.path"
          class="example-card"
          :style="{ '--card-color': example.color }"
        >
          <div class="example-icon">{{ example.icon }}</div>
          <h3>{{ example.title }}</h3>
          <p>{{ example.description }}</p>
          <div class="example-arrow">→</div>
        </NuxtLink>
      </div>
    </section>

    <!-- 底部 -->
    <footer class="footer-section">
      <div class="footer-content">
        <h3>🎉 开始你的 useState 之旅</h3>
        <p>选择一个示例开始学习，或者直接查看文档深入了解。</p>
        <div class="footer-actions">
          <NuxtLink to="/use-state/docs" class="primary-button">
            📖 查看文档
          </NuxtLink>
          <NuxtLink to="/use-state/examples" class="secondary-button">
            🎯 开始练习
          </NuxtLink>
        </div>
      </div>
    </footer>
  </div>
</template>

<style lang="less" scoped>
.learning-center {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 20px;
  text-align: center;

  .hero-title {
    font-size: 3.5em;
    margin: 0 0 20px 0;
    font-weight: 700;

    .emoji {
      display: inline-block;
      animation: bounce 2s infinite;
    }
  }

  .hero-subtitle {
    font-size: 1.3em;
    margin: 0 0 40px 0;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;

    .stat {
      text-align: center;

      .stat-number {
        display: block;
        font-size: 2.5em;
        font-weight: bold;
        line-height: 1;
      }

      .stat-label {
        font-size: 0.9em;
        opacity: 0.8;
      }
    }
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.section-title {
  font-size: 2.5em;
  text-align: center;
  margin: 0 0 50px 0;
  color: #2c3e50;
}

.concepts-section {
  padding: 80px 20px;
  background: #f8f9fa;

  .concepts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
  }

  .concept-card {
    background: white;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }

    .concept-icon {
      font-size: 3em;
      margin-bottom: 20px;
    }

    h3 {
      color: #2c3e50;
      margin: 0 0 15px 0;
    }

    p {
      color: #666;
      line-height: 1.6;
      margin: 0;
    }
  }
}

.quick-start-section {
  padding: 80px 20px;
  background: white;

  .quick-start-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    max-width: 1200px;
    margin: 0 auto;
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 30px;
    }
  }

  .quick-start-text {
    h3 {
      color: #2c3e50;
      font-size: 1.8em;
      margin: 0 0 20px 0;
    }

    p {
      color: #666;
      line-height: 1.6;
      margin: 0 0 20px 0;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        margin: 10px 0;
        color: #27ae60;
        font-weight: 500;
      }
    }
  }

  .quick-start-code {
    background: #2c3e50;
    border-radius: 8px;
    overflow: hidden;

    pre {
      margin: 0;
      padding: 25px;
      color: #ecf0f1;
      overflow-x: auto;

      code {
        font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

.examples-section {
  padding: 80px 20px;
  background: #f8f9fa;

  .examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .example-card {
    background: white;
    padding: 30px;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--card-color);
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(0,0,0,0.15);

      .example-arrow {
        transform: translateX(5px);
      }
    }

    .example-icon {
      font-size: 2.5em;
      margin-bottom: 20px;
    }

    h3 {
      color: #2c3e50;
      margin: 0 0 15px 0;
      font-size: 1.3em;
    }

    p {
      color: #666;
      line-height: 1.6;
      margin: 0 0 20px 0;
    }

    .example-arrow {
      color: var(--card-color);
      font-size: 1.5em;
      font-weight: bold;
      transition: transform 0.3s;
    }
  }
}

.footer-section {
  padding: 80px 20px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  text-align: center;

  .footer-content {
    max-width: 600px;
    margin: 0 auto;

    h3 {
      font-size: 2em;
      margin: 0 0 20px 0;
    }

    p {
      font-size: 1.1em;
      opacity: 0.9;
      margin: 0 0 40px 0;
      line-height: 1.6;
    }
  }

  .footer-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;

    .primary-button, .secondary-button {
      padding: 15px 30px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s;
    }

    .primary-button {
      background: #3498db;
      color: white;

      &:hover {
        background: #2980b9;
        transform: translateY(-2px);
      }
    }

    .secondary-button {
      background: transparent;
      color: white;
      border: 2px solid white;

      &:hover {
        background: white;
        color: #2c3e50;
        transform: translateY(-2px);
      }
    }
  }
}
</style>


<script setup lang="ts">
definePageMeta({
  layout: 'empty',
})

useHead({
  title: 'useFetch 缓存问题演示 - Nuxt 3',
  meta: [
    { name: 'description', content: 'useFetch 缓存机制和解决方案的完整演示' }
  ]
})

// ==================== 问题演示 ====================

// 1. 默认缓存行为（会缓存）
const { data: cachedData, pending: cachedPending } = await useFetch('/api/time')

// 2. 使用 refresh 方法
const { data: refreshData, pending: refreshPending, refresh } = await useFetch('/api/time', {
  key: 'refresh-demo'
})

// 3. 禁用缓存 - 唯一 key
const uniqueKey = ref(Date.now())
const { data: uniqueData, pending: uniquePending } = await useFetch('/api/time', {
  key: computed(() => `unique-${uniqueKey.value}`)
})

// 4. 禁用缓存 - server: false
const { data: clientData, pending: clientPending, execute: executeClient } = await useFetch('/api/time', {
  key: 'client-only',
  server: false,
  immediate: false
})

// 5. 动态参数
const refreshTrigger = ref(0)
const { data: dynamicData, pending: dynamicPending } = await useFetch('/api/time', {
  key: 'dynamic-demo',
  query: {
    _t: refreshTrigger
  }
})

// 6. 使用 $fetch（不缓存）
const manualData = ref(null)
const manualPending = ref(false)

// 7. 使用 execute 方法
const { data: executeData, pending: executePending, execute } = await useFetch('/api/time', {
  key: 'execute-demo',
  immediate: false
})

// ==================== 模拟 API 数据 ====================

// 模拟服务器时间数据
const getServerTime = () => ({
  timestamp: Date.now(),
  time: new Date().toLocaleTimeString(),
  random: Math.floor(Math.random() * 1000)
})

// 模拟数据（实际项目中这些会来自真实的 API）
const simulateApiCall = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(getServerTime())
    }, 500)
  })
}

// ==================== 方法实现 ====================

// 1. 手动刷新
const handleRefresh = async () => {
  await refresh()
}

// 2. 更新唯一 key
const updateUniqueKey = () => {
  uniqueKey.value = Date.now()
}

// 3. 客户端执行
const handleClientExecute = async () => {
  await executeClient()
}

// 4. 动态参数刷新
const handleDynamicRefresh = () => {
  refreshTrigger.value = Date.now()
}

// 5. 手动 $fetch
const handleManualFetch = async () => {
  manualPending.value = true
  try {
    manualData.value = await simulateApiCall()
  } finally {
    manualPending.value = false
  }
}

// 6. Execute 方法
const handleExecute = async () => {
  await execute()
}

// ==================== 初始化模拟数据 ====================

// 为演示目的，设置一些模拟数据
onMounted(() => {
  // 模拟初始数据
  if (!cachedData.value) {
    cachedData.value = getServerTime()
  }
  if (!refreshData.value) {
    refreshData.value = getServerTime()
  }
  if (!uniqueData.value) {
    uniqueData.value = getServerTime()
  }
  if (!dynamicData.value) {
    dynamicData.value = getServerTime()
  }
})

// ==================== 代码示例 ====================

const codeExamples = {
  problem: `// 问题：useFetch 会缓存结果
const { data } = await useFetch('/api/users')

// 即使 API 返回的数据变了，这里的 data 还是第一次的结果
console.log(data.value) // 总是相同的数据`,

  refresh: `// 解决方案1：使用 refresh() 方法
const { data, refresh } = await useFetch('/api/users')

// 手动刷新数据
const handleRefresh = async () => {
  await refresh() // 强制重新请求
}`,

  uniqueKey: `// 解决方案2：使用唯一的 key
const uniqueKey = ref(Date.now())
const { data } = await useFetch('/api/users', {
  key: computed(() => \`users-\${uniqueKey.value}\`)
})

// 更新 key 触发新请求
const forceRefresh = () => {
  uniqueKey.value = Date.now()
}`,

  serverFalse: `// 解决方案3：禁用服务端缓存
const { data, execute } = await useFetch('/api/users', {
  server: false,    // 仅客户端执行
  immediate: false  // 不立即执行
})

// 手动执行
await execute()`,

  dynamicParams: `// 解决方案4：动态参数
const refreshTrigger = ref(0)
const { data } = await useFetch('/api/users', {
  query: {
    _t: refreshTrigger // 时间戳参数
  }
})

// 更新参数触发新请求
const forceRefresh = () => {
  refreshTrigger.value = Date.now()
}`,

  dollarFetch: `// 解决方案5：使用 $fetch（不缓存）
const fetchData = async () => {
  const data = await $fetch('/api/users')
  return data
}

// 每次调用都是新请求
const result = await fetchData()`
}
</script>

<template>
  <div class="cache-demo-container">
    <header class="demo-header">
      <h1>🔄 useFetch 缓存问题演示</h1>
      <p class="subtitle">理解缓存机制并学会正确的解决方案</p>
    </header>

    <!-- 问题说明 -->
    <section class="demo-section">
      <h2>❓ 问题描述</h2>
      <div class="problem-explanation">
        <div class="problem-card">
          <h3>为什么会出现这个问题？</h3>
          <ul>
            <li>🔒 <strong>智能缓存</strong>：useFetch 会缓存相同请求的结果</li>
            <li>⚡ <strong>性能优化</strong>：避免重复请求，提高应用性能</li>
            <li>🔄 <strong>SSR 同步</strong>：服务端和客户端共享缓存</li>
            <li>🎯 <strong>请求去重</strong>：相同 URL 和参数只请求一次</li>
          </ul>
        </div>
        <div class="code-example">
          <h3>问题代码示例</h3>
          <pre><code>{{ codeExamples.problem }}</code></pre>
        </div>
      </div>
    </section>

    <!-- 解决方案演示 -->
    <section class="demo-section">
      <h2>✅ 解决方案演示</h2>
      
      <div class="solutions-grid">
        <!-- 方案1：refresh 方法 -->
        <div class="solution-card">
          <h3>1. 使用 refresh() 方法</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples.refresh }}</code></pre>
          </div>
          <div class="demo-area">
            <div class="data-display">
              <p><strong>状态:</strong> {{ refreshPending ? '加载中...' : '完成' }}</p>
              <p><strong>数据:</strong> {{ refreshData ? JSON.stringify(refreshData) : '无' }}</p>
            </div>
            <button @click="handleRefresh" :disabled="refreshPending">
              {{ refreshPending ? '刷新中...' : '手动刷新' }}
            </button>
          </div>
        </div>

        <!-- 方案2：唯一 key -->
        <div class="solution-card">
          <h3>2. 使用唯一的 key</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples.uniqueKey }}</code></pre>
          </div>
          <div class="demo-area">
            <div class="data-display">
              <p><strong>状态:</strong> {{ uniquePending ? '加载中...' : '完成' }}</p>
              <p><strong>Key:</strong> unique-{{ uniqueKey }}</p>
              <p><strong>数据:</strong> {{ uniqueData ? JSON.stringify(uniqueData) : '无' }}</p>
            </div>
            <button @click="updateUniqueKey" :disabled="uniquePending">
              {{ uniquePending ? '更新中...' : '更新 Key' }}
            </button>
          </div>
        </div>

        <!-- 方案3：server: false -->
        <div class="solution-card">
          <h3>3. 禁用服务端缓存</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples.serverFalse }}</code></pre>
          </div>
          <div class="demo-area">
            <div class="data-display">
              <p><strong>状态:</strong> {{ clientPending ? '加载中...' : '完成' }}</p>
              <p><strong>数据:</strong> {{ clientData ? JSON.stringify(clientData) : '无' }}</p>
            </div>
            <button @click="handleClientExecute" :disabled="clientPending">
              {{ clientPending ? '执行中...' : '客户端执行' }}
            </button>
          </div>
        </div>

        <!-- 方案4：动态参数 -->
        <div class="solution-card">
          <h3>4. 动态参数</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples.dynamicParams }}</code></pre>
          </div>
          <div class="demo-area">
            <div class="data-display">
              <p><strong>状态:</strong> {{ dynamicPending ? '加载中...' : '完成' }}</p>
              <p><strong>参数:</strong> _t={{ refreshTrigger }}</p>
              <p><strong>数据:</strong> {{ dynamicData ? JSON.stringify(dynamicData) : '无' }}</p>
            </div>
            <button @click="handleDynamicRefresh" :disabled="dynamicPending">
              {{ dynamicPending ? '刷新中...' : '动态刷新' }}
            </button>
          </div>
        </div>

        <!-- 方案5：$fetch -->
        <div class="solution-card">
          <h3>5. 使用 $fetch</h3>
          <div class="code-block">
            <pre><code>{{ codeExamples.dollarFetch }}</code></pre>
          </div>
          <div class="demo-area">
            <div class="data-display">
              <p><strong>状态:</strong> {{ manualPending ? '加载中...' : '完成' }}</p>
              <p><strong>数据:</strong> {{ manualData ? JSON.stringify(manualData) : '无' }}</p>
            </div>
            <button @click="handleManualFetch" :disabled="manualPending">
              {{ manualPending ? '获取中...' : '手动 $fetch' }}
            </button>
          </div>
        </div>

        <!-- 方案6：execute 方法 -->
        <div class="solution-card">
          <h3>6. 使用 execute() 方法</h3>
          <div class="demo-area">
            <div class="data-display">
              <p><strong>状态:</strong> {{ executePending ? '加载中...' : '完成' }}</p>
              <p><strong>数据:</strong> {{ executeData ? JSON.stringify(executeData) : '无' }}</p>
            </div>
            <button @click="handleExecute" :disabled="executePending">
              {{ executePending ? '执行中...' : '执行请求' }}
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- 最佳实践建议 -->
    <section class="demo-section">
      <h2>💡 最佳实践建议</h2>
      <div class="best-practices">
        <div class="practice-card">
          <h3>🎯 什么时候需要刷新？</h3>
          <ul>
            <li>用户手动刷新按钮</li>
            <li>数据更新后需要重新获取</li>
            <li>表单提交成功后</li>
            <li>实时数据更新</li>
          </ul>
        </div>
        
        <div class="practice-card">
          <h3>🛠️ 推荐解决方案</h3>
          <ul>
            <li><strong>refresh()</strong> - 最简单直接</li>
            <li><strong>动态参数</strong> - 适合条件刷新</li>
            <li><strong>$fetch</strong> - 适合表单提交</li>
            <li><strong>execute()</strong> - 适合延迟加载</li>
          </ul>
        </div>
        
        <div class="practice-card">
          <h3>⚠️ 注意事项</h3>
          <ul>
            <li>不要过度禁用缓存</li>
            <li>考虑用户体验</li>
            <li>合理使用 loading 状态</li>
            <li>处理错误情况</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 导航 -->
    <div class="navigation">
      <NuxtLink to="/fetch">← 返回 Fetch 中心</NuxtLink>
      <NuxtLink to="/fetch/use-fetch">useFetch 详细指南</NuxtLink>
      <NuxtLink to="/fetch/comparison">API 对比分析</NuxtLink>
    </div>
  </div>
</template>

<style lang="less" scoped>
.cache-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.demo-header {
  text-align: center;
  margin-bottom: 50px;
  padding: 40px 0;
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border-radius: 12px;

  h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
  }

  .subtitle {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
  }
}

.demo-section {
  margin-bottom: 60px;

  h2 {
    color: #2c3e50;
    border-bottom: 3px solid #e74c3c;
    padding-bottom: 10px;
    margin-bottom: 30px;
    font-size: 1.8em;
  }
}

.problem-explanation {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.problem-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);

  h3 {
    color: #e74c3c;
    margin-top: 0;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 8px;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin: 10px 0;
      padding-left: 5px;

      strong {
        color: #2c3e50;
      }
    }
  }
}

.code-example {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);

  h3 {
    color: #2c3e50;
    margin-top: 0;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
  }

  pre {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 10px 0 0 0;

    code {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

.solutions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.solution-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);

  h3 {
    color: #27ae60;
    margin-top: 0;
    border-bottom: 2px solid #27ae60;
    padding-bottom: 8px;
  }

  .code-block {
    margin: 15px 0;

    pre {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 6px;
      overflow-x: auto;
      margin: 0;

      code {
        font-family: 'Monaco', 'Consolas', monospace;
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }

  .demo-area {
    margin-top: 15px;

    .data-display {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 15px;

      p {
        margin: 5px 0;
        font-size: 14px;

        strong {
          color: #2c3e50;
        }
      }
    }

    button {
      width: 100%;
      padding: 10px;
      background: #27ae60;
      color: white;
      border: none;
      border-radius: 6px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;

      &:hover:not(:disabled) {
        background: #229954;
      }

      &:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
      }
    }
  }
}

.best-practices {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.practice-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);

  h3 {
    color: #2c3e50;
    margin-top: 0;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin: 10px 0;
      padding-left: 20px;
      position: relative;
      color: #666;

      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #27ae60;
        font-weight: bold;
      }

      strong {
        color: #2c3e50;
      }
    }
  }
}

.navigation {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin: 50px 0;
  flex-wrap: wrap;

  a {
    padding: 12px 24px;
    background: #e74c3c;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;

    &:hover {
      background: #c0392b;
      transform: translateY(-2px);
    }
  }
}
</style>

<script setup lang="ts">
definePageMeta({
	layout: 'empty',
})

useHead({
	title: 'Fetch API 对比 - Nuxt 3',
	meta: [
		{ name: 'description', content: 'Nuxt 3 中各种 fetch API 的详细对比分析' },
	],
})

// API 对比数据
const apiComparison = [
	{
		api: 'useFetch',
		description: '通用的数据获取组合式函数，适用于大部分场景',
		ssr: true,
		reactive: true,
		cache: true,
		immediate: true,
		useCase: '大部分数据获取场景',
		pros: ['自动 SSR 支持', '响应式参数', '智能缓存', '错误处理', '类型安全'],
		cons: ['默认立即执行', '可能过度获取数据'],
		example: `const { data, pending, error } = await useFetch('/api/users')`,
	},
	{
		api: 'useLazyFetch',
		description: 'useFetch 的懒加载版本，不会阻塞页面导航',
		ssr: false,
		reactive: true,
		cache: true,
		immediate: false,
		useCase: '客户端懒加载数据',
		pros: ['不阻塞导航', '客户端执行', '响应式参数', '智能缓存'],
		cons: ['无 SSR 支持', '首屏可能显示加载状态'],
		example: `const { data, pending } = await useLazyFetch('/api/posts')`,
	},
	{
		api: '$fetch',
		description: '基于 ofetch 的请求函数，提供完全的手动控制',
		ssr: true,
		reactive: false,
		cache: false,
		immediate: false,
		useCase: '手动控制的请求',
		pros: ['完全手动控制', '支持所有 HTTP 方法', '可在任何地方使用', '轻量级'],
		cons: ['无响应式', '无自动缓存', '需要手动错误处理'],
		example: `const data = await $fetch('/api/users', { method: 'POST' })`,
	},
	{
		api: 'useAsyncData',
		description: '自定义异步数据处理，提供最大的灵活性',
		ssr: true,
		reactive: true,
		cache: true,
		immediate: true,
		useCase: '复杂的数据处理逻辑',
		pros: ['最大灵活性', '自定义数据源', '响应式', '智能缓存'],
		cons: ['需要更多代码', '学习成本较高'],
		example: `const { data } = await useAsyncData('users', () => $fetch('/api/users'))`,
	},
]

// 使用场景对比
const useCaseComparison = [
	{
		scenario: '获取用户列表',
		useFetch: '✅ 推荐',
		useLazyFetch: '⚠️ 如需懒加载',
		dollarFetch: '❌ 过于复杂',
		useAsyncData: '⚠️ 如需自定义处理',
	},
	{
		scenario: '搜索功能',
		useFetch: '✅ 推荐',
		useLazyFetch: '✅ 推荐',
		dollarFetch: '⚠️ 手动控制',
		useAsyncData: '⚠️ 复杂搜索逻辑',
	},
	{
		scenario: '表单提交',
		useFetch: '⚠️ 可以但不推荐',
		useLazyFetch: '❌ 不适用',
		dollarFetch: '✅ 推荐',
		useAsyncData: '❌ 过于复杂',
	},
	{
		scenario: '文件上传',
		useFetch: '⚠️ 可以',
		useLazyFetch: '❌ 不适用',
		dollarFetch: '✅ 推荐',
		useAsyncData: '⚠️ 如需进度跟踪',
	},
	{
		scenario: '实时数据',
		useFetch: '❌ 不适用',
		useLazyFetch: '❌ 不适用',
		dollarFetch: '✅ 配合轮询',
		useAsyncData: '✅ 自定义逻辑',
	},
	{
		scenario: '分页数据',
		useFetch: '✅ 推荐',
		useLazyFetch: '✅ 推荐',
		dollarFetch: '⚠️ 手动管理',
		useAsyncData: '⚠️ 复杂分页逻辑',
	},
]

// 性能对比
const performanceComparison = [
	{
		aspect: 'SSR 性能',
		useFetch: '优秀',
		useLazyFetch: '不适用',
		dollarFetch: '优秀',
		useAsyncData: '优秀',
	},
	{
		aspect: '客户端性能',
		useFetch: '良好',
		useLazyFetch: '优秀',
		dollarFetch: '优秀',
		useAsyncData: '良好',
	},
	{
		aspect: '缓存效率',
		useFetch: '优秀',
		useLazyFetch: '优秀',
		dollarFetch: '无',
		useAsyncData: '优秀',
	},
	{
		aspect: '包大小',
		useFetch: '中等',
		useLazyFetch: '中等',
		dollarFetch: '最小',
		useAsyncData: '中等',
	},
]

function getRecommendationClass(recommendation) {
	if (recommendation.includes('✅')) return 'recommended'
	if (recommendation.includes('⚠️')) return 'caution'
	if (recommendation.includes('❌')) return 'not-recommended'
	return ''
}

function getPerformanceClass(performance) {
	if (performance === '优秀' || performance === '最小') return 'excellent'
	if (performance === '良好' || performance === '中等') return 'good'
	if (performance === '无' || performance === '不适用') return 'poor'
	return ''
}
</script>

<template>
	<div class="comparison-container">
		<header class="comparison-header">
			<h1>🔍 Fetch API 详细对比</h1>
			<p class="subtitle">选择最适合你项目需求的数据获取方法</p>
		</header>

		<!-- API 功能对比 -->
		<section class="comparison-section">
			<h2>📊 API 功能对比</h2>
			<div class="api-cards">
				<div v-for="api in apiComparison" :key="api.api" class="api-card">
					<div class="api-header">
						<h3>{{ api.api }}</h3>
						<div class="api-features">
							<span v-if="api.ssr" class="feature ssr">SSR</span>
							<span v-if="api.reactive" class="feature reactive">响应式</span>
							<span v-if="api.cache" class="feature cache">缓存</span>
						</div>
					</div>

					<p class="api-description">{{ api.description }}</p>

					<div class="api-example">
						<h4>示例代码</h4>
						<pre><code>{{ api.example }}</code></pre>
					</div>

					<div class="pros-cons">
						<div class="pros">
							<h4>✅ 优点</h4>
							<ul>
								<li v-for="pro in api.pros" :key="pro">{{ pro }}</li>
							</ul>
						</div>
						<div class="cons">
							<h4>❌ 缺点</h4>
							<ul>
								<li v-for="con in api.cons" :key="con">{{ con }}</li>
							</ul>
						</div>
					</div>

					<div class="use-case">
						<strong>适用场景:</strong> {{ api.useCase }}
					</div>
				</div>
			</div>
		</section>

		<!-- 使用场景对比 -->
		<section class="comparison-section">
			<h2>🎯 使用场景对比</h2>
			<div class="scenario-table">
				<table>
					<thead>
						<tr>
							<th>使用场景</th>
							<th>useFetch</th>
							<th>useLazyFetch</th>
							<th>$fetch</th>
							<th>useAsyncData</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="scenario in useCaseComparison" :key="scenario.scenario">
							<td class="scenario-name">{{ scenario.scenario }}</td>
							<td :class="getRecommendationClass(scenario.useFetch)">
								{{ scenario.useFetch }}
							</td>
							<td :class="getRecommendationClass(scenario.useLazyFetch)">
								{{ scenario.useLazyFetch }}
							</td>
							<td :class="getRecommendationClass(scenario.dollarFetch)">
								{{ scenario.dollarFetch }}
							</td>
							<td :class="getRecommendationClass(scenario.useAsyncData)">
								{{ scenario.useAsyncData }}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</section>

		<!-- 性能对比 -->
		<section class="comparison-section">
			<h2>⚡ 性能对比</h2>
			<div class="performance-table">
				<table>
					<thead>
						<tr>
							<th>性能方面</th>
							<th>useFetch</th>
							<th>useLazyFetch</th>
							<th>$fetch</th>
							<th>useAsyncData</th>
						</tr>
					</thead>
					<tbody>
						<tr v-for="perf in performanceComparison" :key="perf.aspect">
							<td class="aspect-name">{{ perf.aspect }}</td>
							<td :class="getPerformanceClass(perf.useFetch)">
								{{ perf.useFetch }}
							</td>
							<td :class="getPerformanceClass(perf.useLazyFetch)">
								{{ perf.useLazyFetch }}
							</td>
							<td :class="getPerformanceClass(perf.dollarFetch)">
								{{ perf.dollarFetch }}
							</td>
							<td :class="getPerformanceClass(perf.useAsyncData)">
								{{ perf.useAsyncData }}
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</section>

		<!-- 决策流程图 -->
		<section class="comparison-section">
			<h2>🤔 如何选择？</h2>
			<div class="decision-flow">
				<div class="decision-step">
					<div class="question">你需要什么类型的数据获取？</div>
					<div class="decision-branches">
						<div class="branch">
							<div class="answer">获取数据显示</div>
							<div class="sub-question">需要 SSR 吗？</div>
							<div class="sub-branches">
								<div class="sub-branch">
									<div class="answer yes">是</div>
									<div class="recommendation">使用 useFetch</div>
								</div>
								<div class="sub-branch">
									<div class="answer no">否</div>
									<div class="recommendation">使用 useLazyFetch</div>
								</div>
							</div>
						</div>
						<div class="branch">
							<div class="answer">提交数据</div>
							<div class="recommendation">使用 $fetch</div>
						</div>
						<div class="branch">
							<div class="answer">复杂数据处理</div>
							<div class="recommendation">使用 useAsyncData</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- 最佳实践建议 -->
		<section class="comparison-section">
			<h2>✅ 最佳实践建议</h2>
			<div class="best-practices">
				<div class="practice-card">
					<h3>🎯 选择原则</h3>
					<ul>
						<li>默认使用 <code>useFetch</code> 进行数据获取</li>
						<li>需要懒加载时使用 <code>useLazyFetch</code></li>
						<li>表单提交和手动请求使用 <code>$fetch</code></li>
						<li>复杂数据处理使用 <code>useAsyncData</code></li>
					</ul>
				</div>

				<div class="practice-card">
					<h3>⚡ 性能优化</h3>
					<ul>
						<li>合理使用 <code>key</code> 参数进行缓存</li>
						<li>避免在循环中使用 fetch API</li>
						<li>使用 <code>transform</code> 减少数据传输</li>
						<li>善用 <code>watch</code> 控制请求时机</li>
					</ul>
				</div>

				<div class="practice-card">
					<h3>🛡️ 错误处理</h3>
					<ul>
						<li>总是检查 <code>error</code> 状态</li>
						<li>提供合适的加载状态</li>
						<li>实现重试机制</li>
						<li>使用 <code>onResponseError</code> 统一处理错误</li>
					</ul>
				</div>
			</div>
		</section>

		<!-- 导航 -->
		<div class="navigation">
			<NuxtLink to="/fetch">← 返回 Fetch 中心</NuxtLink>
			<NuxtLink to="/fetch/use-fetch">useFetch 详细指南</NuxtLink>
		</div>
	</div>
</template>


<style lang="less" scoped>
.comparison-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	line-height: 1.6;
}

.comparison-header {
	text-align: center;
	margin-bottom: 50px;
	padding: 40px 0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 12px;

	h1 {
		margin: 0 0 10px 0;
		font-size: 2.5em;
	}

	.subtitle {
		margin: 0;
		font-size: 1.2em;
		opacity: 0.9;
	}
}

.comparison-section {
	margin-bottom: 60px;

	h2 {
		color: #2c3e50;
		border-bottom: 3px solid #3498db;
		padding-bottom: 10px;
		margin-bottom: 30px;
		font-size: 1.8em;
	}
}

.api-cards {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
	gap: 25px;
}

.api-card {
	background: white;
	border: 1px solid #e1e5e9;
	border-radius: 12px;
	padding: 25px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

	.api-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;

		h3 {
			margin: 0;
			color: #2c3e50;
			font-size: 1.3em;
		}

		.api-features {
			display: flex;
			gap: 5px;

			.feature {
				padding: 2px 8px;
				border-radius: 12px;
				font-size: 0.7em;
				font-weight: 600;
				color: white;

				&.ssr {
					background: #3498db;
				}
				&.reactive {
					background: #27ae60;
				}
				&.cache {
					background: #f39c12;
				}
			}
		}
	}

	.api-description {
		color: #666;
		margin-bottom: 20px;
	}

	.api-example {
		margin-bottom: 20px;

		h4 {
			margin: 0 0 10px 0;
			color: #2c3e50;
			font-size: 1em;
		}

		pre {
			background: #2c3e50;
			color: #ecf0f1;
			padding: 15px;
			border-radius: 6px;
			overflow-x: auto;
			margin: 0;

			code {
				font-family: 'Monaco', 'Consolas', monospace;
				font-size: 12px;
			}
		}
	}

	.pros-cons {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 15px;
		margin-bottom: 20px;

		.pros,
		.cons {
			h4 {
				margin: 0 0 10px 0;
				font-size: 0.9em;
			}

			ul {
				list-style: none;
				padding: 0;
				margin: 0;

				li {
					margin: 5px 0;
					font-size: 0.8em;
					color: #666;
				}
			}
		}
	}

	.use-case {
		background: #f8f9fa;
		padding: 10px;
		border-radius: 6px;
		font-size: 0.9em;
		color: #2c3e50;
	}
}

.scenario-table,
.performance-table {
	overflow-x: auto;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	border-radius: 8px;

	table {
		width: 100%;
		border-collapse: collapse;
		background: white;

		th,
		td {
			padding: 12px;
			text-align: center;
			border-bottom: 1px solid #e1e5e9;
		}

		th {
			background: #f8f9fa;
			font-weight: 600;
			color: #2c3e50;
		}

		.scenario-name,
		.aspect-name {
			text-align: left;
			font-weight: 500;
		}

		.recommended {
			color: #27ae60;
			font-weight: 600;
		}
		.caution {
			color: #f39c12;
			font-weight: 600;
		}
		.not-recommended {
			color: #e74c3c;
			font-weight: 600;
		}

		.excellent {
			color: #27ae60;
			font-weight: 600;
		}
		.good {
			color: #3498db;
			font-weight: 600;
		}
		.poor {
			color: #95a5a6;
		}
	}
}

.decision-flow {
	background: white;
	border: 2px solid #e1e5e9;
	border-radius: 12px;
	padding: 30px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

	.question {
		background: #3498db;
		color: white;
		padding: 15px;
		border-radius: 8px;
		text-align: center;
		font-weight: 600;
		margin-bottom: 20px;
	}

	.decision-branches {
		display: flex;
		gap: 20px;
		justify-content: center;
		flex-wrap: wrap;
	}

	.branch {
		text-align: center;
		flex: 1;
		min-width: 200px;
	}

	.answer {
		background: #27ae60;
		color: white;
		padding: 10px;
		border-radius: 6px;
		font-weight: 600;
		margin-bottom: 15px;
	}

	.sub-question {
		background: #9b59b6;
		color: white;
		padding: 8px;
		border-radius: 4px;
		font-size: 0.9em;
		margin: 10px 0;
	}

	.sub-branches {
		display: flex;
		gap: 10px;
		justify-content: center;
	}

	.sub-branch {
		flex: 1;
	}

	.yes {
		background: #27ae60;
	}
	.no {
		background: #e74c3c;
	}

	.recommendation {
		background: #f8f9fa;
		padding: 10px;
		border-radius: 6px;
		border: 2px solid #3498db;
		font-weight: 600;
		color: #2c3e50;
		margin-top: 10px;
	}
}

.best-practices {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
	gap: 25px;
}

.practice-card {
	background: white;
	border: 1px solid #e1e5e9;
	border-radius: 8px;
	padding: 20px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

	h3 {
		margin-top: 0;
		color: #2c3e50;
		border-bottom: 2px solid #3498db;
		padding-bottom: 8px;
	}

	ul {
		list-style: none;
		padding: 0;

		li {
			margin: 10px 0;
			padding-left: 20px;
			position: relative;
			color: #666;

			&::before {
				content: '✓';
				position: absolute;
				left: 0;
				color: #27ae60;
				font-weight: bold;
			}

			code {
				background: #f8f9fa;
				color: #e74c3c;
				padding: 2px 6px;
				border-radius: 4px;
				font-size: 0.9em;
			}
		}
	}
}

.navigation {
	display: flex;
	gap: 20px;
	justify-content: center;
	margin: 50px 0;
	flex-wrap: wrap;

	a {
		padding: 12px 24px;
		background: #3498db;
		color: white;
		text-decoration: none;
		border-radius: 8px;
		font-weight: 500;
		transition: all 0.3s;

		&:hover {
			background: #2980b9;
			transform: translateY(-2px);
		}
	}
}
</style>

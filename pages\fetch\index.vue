<script setup lang="ts">
definePageMeta({
  layout: 'empty',
})

useHead({
  title: 'Fetch API 学习中心 - Nuxt 3',
  meta: [
    { name: 'description', content: 'Nuxt 3 中所有 fetch 相关 API 的完整学习资源' }
  ]
})

// 学习资源列表
const fetchResources = [
  {
    title: '📚 useFetch 详细指南',
    description: '学习 useFetch 的基础用法和高级特性',
    path: '/fetch/use-fetch',
    icon: '🎯',
    color: '#3498db',
    difficulty: '基础'
  },
  {
    title: '⚡ useLazyFetch 指南',
    description: '掌握懒加载数据获取的最佳实践',
    path: '/fetch/use-lazy-fetch',
    icon: '⏳',
    color: '#27ae60',
    difficulty: '中级'
  },
  {
    title: '🔧 $fetch 手动控制',
    description: '学习如何手动控制数据请求',
    path: '/fetch/dollar-fetch',
    icon: '🛠️',
    color: '#e74c3c',
    difficulty: '中级'
  },
  {
    title: '📊 useAsyncData 深入',
    description: '自定义异步数据处理的高级用法',
    path: '/fetch/use-async-data',
    icon: '📈',
    color: '#f39c12',
    difficulty: '高级'
  },
  {
    title: '🔄 响应式参数',
    description: '学习如何使用响应式参数进行动态请求',
    path: '/fetch/reactive-params',
    icon: '🔄',
    color: '#9b59b6',
    difficulty: '中级'
  },
  {
    title: '⚠️ 错误处理',
    description: '掌握各种错误处理和重试机制',
    path: '/fetch/error-handling',
    icon: '🛡️',
    color: '#e67e22',
    difficulty: '高级'
  },
  {
    title: '🚀 性能优化',
    description: '学习缓存、预加载等性能优化技巧',
    path: '/fetch/performance',
    icon: '⚡',
    color: '#1abc9c',
    difficulty: '高级'
  },
  {
    title: '🔍 API 对比分析',
    description: '详细对比各种 fetch API 的特点和使用场景',
    path: '/fetch/comparison',
    icon: '⚖️',
    color: '#8e44ad',
    difficulty: '中级'
  },
  {
    title: '🎮 实战示例',
    description: '通过实际项目学习 fetch API 的应用',
    path: '/fetch/examples',
    icon: '🎯',
    color: '#34495e',
    difficulty: '实战'
  }
]

// API 对比数据
const apiComparison = [
  {
    api: 'useFetch',
    description: '通用的数据获取组合式函数',
    ssr: true,
    reactive: true,
    cache: true,
    useCase: '大部分数据获取场景'
  },
  {
    api: 'useLazyFetch',
    description: '懒加载版本的 useFetch',
    ssr: false,
    reactive: true,
    cache: true,
    useCase: '客户端懒加载数据'
  },
  {
    api: '$fetch',
    description: '基于 ofetch 的请求函数',
    ssr: true,
    reactive: false,
    cache: false,
    useCase: '手动控制的请求'
  },
  {
    api: 'useAsyncData',
    description: '自定义异步数据处理',
    ssr: true,
    reactive: true,
    cache: true,
    useCase: '复杂的数据处理逻辑'
  }
]

// 快速开始代码示例
const quickStartCode = `// 1. 基础 useFetch
const { data, pending, error } = await useFetch('/api/users')

// 2. 带参数的请求
const userId = ref(1)
const { data: user } = await useFetch(\`/api/users/\${userId.value}\`)

// 3. POST 请求
const { data, execute } = await useFetch('/api/posts', {
  method: 'POST',
  body: { title: 'New Post' },
  immediate: false
})

// 4. 懒加载
const { data } = await useLazyFetch('/api/lazy-data')

// 5. 手动 fetch
const data = await $fetch('/api/manual')

// 6. 自定义异步数据
const { data } = await useAsyncData('custom', () => {
  return $fetch('/api/custom')
})`

// 学习路径
const learningPath = [
  {
    step: 1,
    title: '基础概念',
    description: '了解 Nuxt 3 中的数据获取概念',
    resources: ['useFetch 详细指南']
  },
  {
    step: 2,
    title: '核心 API',
    description: '掌握主要的 fetch API',
    resources: ['useFetch', 'useLazyFetch', '$fetch']
  },
  {
    step: 3,
    title: '高级特性',
    description: '学习响应式参数和错误处理',
    resources: ['响应式参数', '错误处理']
  },
  {
    step: 4,
    title: '性能优化',
    description: '掌握缓存和性能优化技巧',
    resources: ['性能优化', 'useAsyncData']
  },
  {
    step: 5,
    title: '实战应用',
    description: '在实际项目中应用所学知识',
    resources: ['实战示例']
  }
]
</script>

<template>
  <div class="fetch-center">
    <!-- 头部 -->
    <header class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="emoji">🚀</span>
          Fetch API 学习中心
        </h1>
        <p class="hero-subtitle">
          掌握 Nuxt 3 中所有的数据获取方法和最佳实践
        </p>
        <div class="hero-stats">
          <div class="stat">
            <span class="stat-number">9</span>
            <span class="stat-label">个专题</span>
          </div>
          <div class="stat">
            <span class="stat-number">4</span>
            <span class="stat-label">个 API</span>
          </div>
          <div class="stat">
            <span class="stat-number">∞</span>
            <span class="stat-label">种应用</span>
          </div>
        </div>
      </div>
    </header>

    <!-- API 概览 -->
    <section class="api-overview-section">
      <h2 class="section-title">📊 API 概览对比</h2>
      <div class="api-comparison-table">
        <table>
          <thead>
            <tr>
              <th>API</th>
              <th>描述</th>
              <th>SSR</th>
              <th>响应式</th>
              <th>缓存</th>
              <th>适用场景</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="api in apiComparison" :key="api.api">
              <td class="api-name"><code>{{ api.api }}</code></td>
              <td>{{ api.description }}</td>
              <td class="status">{{ api.ssr ? '✅' : '❌' }}</td>
              <td class="status">{{ api.reactive ? '✅' : '❌' }}</td>
              <td class="status">{{ api.cache ? '✅' : '❌' }}</td>
              <td class="use-case">{{ api.useCase }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>

    <!-- 快速开始 -->
    <section class="quick-start-section">
      <h2 class="section-title">⚡ 快速开始</h2>
      <div class="quick-start-content">
        <div class="quick-start-text">
          <h3>立即开始使用 Fetch API</h3>
          <p>Nuxt 3 提供了多种数据获取方法，每种都有其特定的用途和优势。选择合适的 API 可以让你的应用更高效、更易维护。</p>
          <ul>
            <li>🎯 <strong>useFetch</strong> - 最常用的数据获取方法</li>
            <li>⏳ <strong>useLazyFetch</strong> - 客户端懒加载</li>
            <li>🔧 <strong>$fetch</strong> - 手动控制请求</li>
            <li>📊 <strong>useAsyncData</strong> - 自定义异步处理</li>
          </ul>
        </div>
        <div class="quick-start-code">
          <pre><code>{{ quickStartCode }}</code></pre>
        </div>
      </div>
    </section>

    <!-- 学习资源 -->
    <section class="resources-section">
      <h2 class="section-title">📚 学习资源</h2>
      <div class="resources-grid">
        <NuxtLink 
          v-for="resource in fetchResources" 
          :key="resource.title"
          :to="resource.path"
          class="resource-card"
          :style="{ '--card-color': resource.color }"
        >
          <div class="resource-header">
            <div class="resource-icon">{{ resource.icon }}</div>
            <div class="resource-difficulty">{{ resource.difficulty }}</div>
          </div>
          <h3>{{ resource.title }}</h3>
          <p>{{ resource.description }}</p>
          <div class="resource-arrow">→</div>
        </NuxtLink>
      </div>
    </section>

    <!-- 学习路径 -->
    <section class="learning-path-section">
      <h2 class="section-title">🛤️ 推荐学习路径</h2>
      <div class="learning-path">
        <div v-for="step in learningPath" :key="step.step" class="path-step">
          <div class="step-number">{{ step.step }}</div>
          <div class="step-content">
            <h3>{{ step.title }}</h3>
            <p>{{ step.description }}</p>
            <div class="step-resources">
              <span v-for="resource in step.resources" :key="resource" class="resource-tag">
                {{ resource }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 最佳实践 -->
    <section class="best-practices-section">
      <h2 class="section-title">✅ 最佳实践</h2>
      <div class="practices-grid">
        <div class="practice-card">
          <h3>🎯 选择合适的 API</h3>
          <ul>
            <li>大部分场景使用 <code>useFetch</code></li>
            <li>客户端懒加载使用 <code>useLazyFetch</code></li>
            <li>手动控制使用 <code>$fetch</code></li>
            <li>复杂逻辑使用 <code>useAsyncData</code></li>
          </ul>
        </div>
        
        <div class="practice-card">
          <h3>⚡ 性能优化</h3>
          <ul>
            <li>合理使用缓存和 key</li>
            <li>避免不必要的重复请求</li>
            <li>使用 transform 处理数据</li>
            <li>善用 watch 参数控制更新</li>
          </ul>
        </div>
        
        <div class="practice-card">
          <h3>🛡️ 错误处理</h3>
          <ul>
            <li>总是处理 error 状态</li>
            <li>提供合适的 loading 状态</li>
            <li>使用 onResponseError 处理错误</li>
            <li>实现重试机制</li>
          </ul>
        </div>
        
        <div class="practice-card">
          <h3>🔧 开发技巧</h3>
          <ul>
            <li>使用 TypeScript 类型定义</li>
            <li>合理组织 API 端点</li>
            <li>使用环境变量配置</li>
            <li>编写可复用的组合式函数</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 底部导航 -->
    <footer class="footer-section">
      <div class="footer-content">
        <h3>🎉 开始你的 Fetch API 学习之旅</h3>
        <p>选择一个主题开始学习，或者直接查看实战示例。</p>
        <div class="footer-actions">
          <NuxtLink to="/fetch/use-fetch" class="primary-button">
            🎯 开始学习 useFetch
          </NuxtLink>
          <NuxtLink to="/fetch/examples" class="secondary-button">
            🎮 查看实战示例
          </NuxtLink>
        </div>
      </div>
    </footer>
  </div>
</template>

<style lang="less" scoped>
.fetch-center {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 20px;
  text-align: center;

  .hero-title {
    font-size: 3.5em;
    margin: 0 0 20px 0;
    font-weight: 700;

    .emoji {
      display: inline-block;
      animation: bounce 2s infinite;
    }
  }

  .hero-subtitle {
    font-size: 1.3em;
    margin: 0 0 40px 0;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .hero-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;

    .stat {
      text-align: center;

      .stat-number {
        display: block;
        font-size: 2.5em;
        font-weight: bold;
        line-height: 1;
      }

      .stat-label {
        font-size: 0.9em;
        opacity: 0.8;
      }
    }
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.section-title {
  font-size: 2.5em;
  text-align: center;
  margin: 0 0 50px 0;
  color: #2c3e50;
}

.api-overview-section {
  padding: 80px 20px;
  background: #f8f9fa;

  .api-comparison-table {
    max-width: 1200px;
    margin: 0 auto;
    overflow-x: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-radius: 12px;

    table {
      width: 100%;
      border-collapse: collapse;
      background: white;

      th, td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #e1e5e9;
      }

      th {
        background: #f8f9fa;
        font-weight: 600;
        color: #2c3e50;
      }

      .api-name code {
        background: #e3f2fd;
        color: #1976d2;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 600;
      }

      .status {
        text-align: center;
        font-size: 1.2em;
      }

      .use-case {
        color: #666;
        font-style: italic;
      }
    }
  }
}

.quick-start-section {
  padding: 80px 20px;
  background: white;

  .quick-start-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    max-width: 1200px;
    margin: 0 auto;
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 30px;
    }
  }

  .quick-start-text {
    h3 {
      color: #2c3e50;
      font-size: 1.8em;
      margin: 0 0 20px 0;
    }

    p {
      color: #666;
      line-height: 1.6;
      margin: 0 0 20px 0;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        margin: 10px 0;
        color: #2c3e50;
        font-weight: 500;

        strong {
          color: #3498db;
        }
      }
    }
  }

  .quick-start-code {
    background: #2c3e50;
    border-radius: 12px;
    overflow: hidden;

    pre {
      margin: 0;
      padding: 25px;
      color: #ecf0f1;
      overflow-x: auto;

      code {
        font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

.resources-section {
  padding: 80px 20px;
  background: #f8f9fa;

  .resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .resource-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--card-color);
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 30px rgba(0,0,0,0.15);

      .resource-arrow {
        transform: translateX(5px);
      }
    }

    .resource-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      .resource-icon {
        font-size: 2em;
      }

      .resource-difficulty {
        background: var(--card-color);
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: 600;
      }
    }

    h3 {
      color: #2c3e50;
      margin: 0 0 10px 0;
      font-size: 1.2em;
    }

    p {
      color: #666;
      line-height: 1.6;
      margin: 0 0 15px 0;
    }

    .resource-arrow {
      color: var(--card-color);
      font-size: 1.5em;
      font-weight: bold;
      transition: transform 0.3s;
    }
  }
}

.learning-path-section {
  padding: 80px 20px;
  background: white;

  .learning-path {
    max-width: 800px;
    margin: 0 auto;
  }

  .path-step {
    display: flex;
    align-items: flex-start;
    gap: 25px;
    margin-bottom: 40px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #3498db;

    .step-number {
      width: 40px;
      height: 40px;
      background: #3498db;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      flex-shrink: 0;
    }

    .step-content {
      flex: 1;

      h3 {
        color: #2c3e50;
        margin: 0 0 10px 0;
      }

      p {
        color: #666;
        line-height: 1.6;
        margin: 0 0 15px 0;
      }

      .step-resources {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        .resource-tag {
          background: #e3f2fd;
          color: #1976d2;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 0.8em;
          font-weight: 500;
        }
      }
    }
  }
}

.best-practices-section {
  padding: 80px 20px;
  background: #f8f9fa;

  .practices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .practice-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);

    h3 {
      color: #2c3e50;
      margin: 0 0 15px 0;
      border-bottom: 2px solid #3498db;
      padding-bottom: 10px;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        margin: 10px 0;
        padding-left: 20px;
        position: relative;
        color: #666;

        &::before {
          content: '✓';
          position: absolute;
          left: 0;
          color: #27ae60;
          font-weight: bold;
        }

        code {
          background: #f8f9fa;
          color: #e74c3c;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 0.9em;
        }
      }
    }
  }
}

.footer-section {
  padding: 80px 20px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  text-align: center;

  .footer-content {
    max-width: 600px;
    margin: 0 auto;

    h3 {
      font-size: 2em;
      margin: 0 0 20px 0;
    }

    p {
      font-size: 1.1em;
      opacity: 0.9;
      margin: 0 0 40px 0;
      line-height: 1.6;
    }
  }

  .footer-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;

    .primary-button, .secondary-button {
      padding: 15px 30px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s;
    }

    .primary-button {
      background: #3498db;
      color: white;

      &:hover {
        background: #2980b9;
        transform: translateY(-2px);
      }
    }

    .secondary-button {
      background: transparent;
      color: white;
      border: 2px solid white;

      &:hover {
        background: white;
        color: #2c3e50;
        transform: translateY(-2px);
      }
    }
  }
}
</style>

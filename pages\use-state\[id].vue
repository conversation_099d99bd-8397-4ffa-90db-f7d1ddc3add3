<script setup lang="ts">
definePageMeta({
  layout:'empty',
})

// 获取路由参数
const route = useRoute()
const userId = route.params.id

// 定义用户类型
interface User {
  id: string
  name: string
  email: string
  address: string
  age: number
}

// 使用 useState 管理用户状态，提供初始值
const user = useState<User>(`user${userId}`, () => ({
  id: userId as string,
  name: '',
  email: '',
  address: '',
  age: 0
}))

// 模拟从 API 获取用户数据
// const fetchUser = async () => {
//   // 模拟 API 调用
//   await new Promise(resolve => setTimeout(resolve, 300))

//   // 模拟用户数据
//   const userData = {
//     id: userId as string,
//     name: `用户 ${userId}`,
//     email: `user${userId}@example.com`,
//     address: `地址 ${userId}`,
//     age: 20 + parseInt(userId as string)
//   }

//   // 更新状态
//   user.value = userData
// }

// 页面加载时获取用户数据
onMounted(() => {
  if (userId) {

    console.log('%c [  ]-46', 'font-size:13px; background:#c07436; color:#ffb87a;', userId);
    // fetchUser()
  }
})
</script>

<template>
  <div class="user-detail">
  {{ user }}

    <h1>用户详情页 - ID: {{ userId }}</h1>

    <div class="user-info">
      <h2>用户信息</h2>

      <div class="form-group">
        <label>用户ID:</label>
        <span>{{ user.id }}</span>
      </div>

      <div class="form-group">
        <label>姓名:</label>
        <input
          v-model="user.name"
          type="text"
          placeholder="请输入姓名"
        >
      </div>

      <div class="form-group">
        <label>邮箱:</label>
        <input
          v-model="user.email"
          type="email"
          placeholder="请输入邮箱"
        >
      </div>

      <div class="form-group">
        <label>地址:</label>
        <input
          v-model="user.address"
          type="text"
          placeholder="请输入地址"
        >
      </div>

      <div class="form-group">
        <label>年龄:</label>
        <input
          v-model="user.age"
          type="number"
          placeholder="请输入年龄"
        >
      </div>
    </div>

    <div class="debug-info">
      <h3>状态调试</h3>
      <pre>{{ user }}</pre>
    </div>

    <div class="navigation">
      <NuxtLink to="/use-state/docs">📚 查看文档</NuxtLink>
      <NuxtLink to="/use-state/examples">📝 基础示例</NuxtLink>
      <NuxtLink to="/use-state/advanced">🚀 高级示例</NuxtLink>
      <NuxtLink to="/use-state/10">跳转到用户10</NuxtLink>
      <NuxtLink to="/use-state/9">跳转到用户9</NuxtLink>
    </div>
  </div>
</template>

<style lang="less" scoped>
.user-detail {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.user-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  border: 1px solid #e9ecef;
}

.form-group {
  margin: 15px 0;
  display: flex;
  align-items: center;

  label {
    width: 80px;
    font-weight: bold;
    color: #2c3e50;
  }

  input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-left: 10px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #3498db;
      box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    }
  }

  span {
    margin-left: 10px;
    color: #666;
    font-family: monospace;
  }
}

.debug-info {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;

  h3 {
    margin-top: 0;
    color: #3498db;
  }

  pre {
    background: #34495e;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
    line-height: 1.4;
  }
}

.navigation {
  display: flex;
  gap: 15px;
  margin: 30px 0;
  flex-wrap: wrap;

  a {
    padding: 10px 20px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    transition: background 0.3s;

    &:hover {
      background: #2980b9;
    }
  }
}

h1 {
  color: #2c3e50;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

h2 {
  color: #34495e;
  margin-top: 0;
}
</style>
<script setup lang="ts">
definePageMeta({
  layout: 'empty',
})

// ==================== Vue 3 ref/reactive 示例 ====================

// 1. Vue 3 ref - 组件级别状态
const vueCounter = ref(0)
const vueUser = ref({
  name: 'Vue User',
  age: 25
})

// 2. Vue 3 reactive - 组件级别状态
const vueState = reactive({
  theme: 'light',
  todos: ['学习 Vue 3', '理解响应式'],
  settings: {
    language: 'zh',
    notifications: true
  }
})

// ==================== Nuxt 3 useState 示例 ====================

// 1. useState - 应用级别状态
const nuxtCounter = useState<number>('nuxt-counter', () => 0)
const nuxtUser = useState('nuxt-user', () => ({
  name: 'Nuxt User',
  age: 30
}))

// 2. useState - 复杂状态
interface AppState {
  theme: 'light' | 'dark'
  todos: string[]
  settings: {
    language: string
    notifications: boolean
  }
}

const nuxtState = useState<AppState>('nuxt-state', () => ({
  theme: 'light',
  todos: ['学习 Nuxt 3', '理解 useState'],
  settings: {
    language: 'zh',
    notifications: true
  }
}))

// ==================== 操作方法 ====================

// Vue 3 操作方法
const incrementVueCounter = () => {
  vueCounter.value++
}

const updateVueUser = () => {
  vueUser.value.name = `Vue User ${Date.now()}`
  vueUser.value.age++
}

const addVueTodo = () => {
  vueState.todos.push(`Vue 任务 ${vueState.todos.length + 1}`)
}

const toggleVueTheme = () => {
  vueState.theme = vueState.theme === 'light' ? 'dark' : 'light'
}

// Nuxt 3 操作方法
const incrementNuxtCounter = () => {
  nuxtCounter.value++
}

const updateNuxtUser = () => {
  if (nuxtUser.value) {
    nuxtUser.value.name = `Nuxt User ${Date.now()}`
    nuxtUser.value.age++
  }
}

const addNuxtTodo = () => {
  if (nuxtState.value) {
    nuxtState.value.todos.push(`Nuxt 任务 ${nuxtState.value.todos.length + 1}`)
  }
}

const toggleNuxtTheme = () => {
  if (nuxtState.value) {
    nuxtState.value.theme = nuxtState.value.theme === 'light' ? 'dark' : 'light'
  }
}

// ==================== 生命周期演示 ====================

// 组件挂载时的状态
const mountTime = ref(new Date().toLocaleTimeString())
const mountTimeGlobal = useState('mount-time', () => new Date().toLocaleTimeString())

// 监听状态变化
watch(vueCounter, (newVal) => {
  console.log('Vue Counter 变化:', newVal)
})

watch(nuxtCounter, (newVal) => {
  console.log('Nuxt Counter 变化:', newVal)
})

// ==================== 持久化演示 ====================

// Vue 3 需要手动处理持久化
const vuePersistentData = ref('')

onMounted(() => {
  if (import.meta.client) {
    vuePersistentData.value = localStorage.getItem('vue-data') || ''
  }
})

watch(vuePersistentData, (newVal) => {
  if (import.meta.client) {
    localStorage.setItem('vue-data', newVal)
  }
})

// Nuxt 3 可以结合 useState 实现持久化
const nuxtPersistentData = useState('nuxt-persistent', () => '')

watch(nuxtPersistentData, (newVal) => {
  if (import.meta.client) {
    localStorage.setItem('nuxt-data', newVal)
  }
})

onMounted(() => {
  if (import.meta.client) {
    const saved = localStorage.getItem('nuxt-data')
    if (saved) {
      nuxtPersistentData.value = saved
    }
  }
})

// ==================== 计算属性对比 ====================

// Vue 3 计算属性
const vueComputedInfo = computed(() => {
  return `Vue: ${vueUser.value.name} (${vueUser.value.age}岁) - ${vueState.todos.length} 个任务`
})

// Nuxt 3 计算属性
const nuxtComputedInfo = computed(() => {
  if (!nuxtUser.value || !nuxtState.value) return ''
  return `Nuxt: ${nuxtUser.value.name} (${nuxtUser.value.age}岁) - ${nuxtState.value.todos.length} 个任务`
})
</script>

<template>
  <div class="comparison-container">
    <h1>🔍 useState vs ref/reactive 详细对比</h1>

    <!-- 基础概念对比 -->
    <section class="concept-section">
      <h2>📚 基础概念对比</h2>
      <div class="concept-grid">
        <div class="concept-card vue-card">
          <h3>Vue 3 ref/reactive</h3>
          <ul>
            <li>🏠 组件级别状态</li>
            <li>🔄 组件销毁时状态消失</li>
            <li>🎯 每个组件实例独立</li>
            <li>⚙️ 需要手动处理 SSR</li>
            <li>💾 需要手动持久化</li>
          </ul>
        </div>
        <div class="concept-card nuxt-card">
          <h3>Nuxt 3 useState</h3>
          <ul>
            <li>🌐 应用级别状态</li>
            <li>♾️ 应用生命周期内持续</li>
            <li>🤝 全局共享状态</li>
            <li>🔄 自动处理 SSR 同步</li>
            <li>🔧 可结合其他工具持久化</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 计数器对比 -->
    <section class="demo-section">
      <h2>🔢 计数器对比</h2>
      <div class="demo-grid">
        <div class="demo-card vue-card">
          <h3>Vue 3 ref</h3>
          <p>当前值: <strong>{{ vueCounter }}</strong></p>
          <p class="note">组件级别 - 刷新页面会重置</p>
          <button @click="incrementVueCounter">+1</button>
        </div>
        <div class="demo-card nuxt-card">
          <h3>Nuxt 3 useState</h3>
          <p>当前值: <strong>{{ nuxtCounter }}</strong></p>
          <p class="note">全局级别 - 在其他页面也能看到</p>
          <button @click="incrementNuxtCounter">+1</button>
        </div>
      </div>
    </section>

    <!-- 用户信息对比 -->
    <section class="demo-section">
      <h2>👤 用户信息对比</h2>
      <div class="demo-grid">
        <div class="demo-card vue-card">
          <h3>Vue 3 ref</h3>
          <p>{{ vueComputedInfo }}</p>
          <button @click="updateVueUser">更新用户</button>
          <button @click="addVueTodo">添加任务</button>
          <button @click="toggleVueTheme">切换主题</button>
          <p class="theme-info">当前主题: {{ vueState.theme }}</p>
        </div>
        <div class="demo-card nuxt-card">
          <h3>Nuxt 3 useState</h3>
          <p>{{ nuxtComputedInfo }}</p>
          <button @click="updateNuxtUser">更新用户</button>
          <button @click="addNuxtTodo">添加任务</button>
          <button @click="toggleNuxtTheme">切换主题</button>
          <p class="theme-info">当前主题: {{ nuxtState?.theme }}</p>
        </div>
      </div>
    </section>

    <!-- 生命周期对比 -->
    <section class="demo-section">
      <h2>⏰ 生命周期对比</h2>
      <div class="demo-grid">
        <div class="demo-card vue-card">
          <h3>Vue 3 组件挂载时间</h3>
          <p>{{ mountTime }}</p>
          <p class="note">每次进入组件都会更新</p>
        </div>
        <div class="demo-card nuxt-card">
          <h3>Nuxt 3 全局挂载时间</h3>
          <p>{{ mountTimeGlobal }}</p>
          <p class="note">只在首次创建时设置</p>
        </div>
      </div>
    </section>

    <!-- 持久化对比 -->
    <section class="demo-section">
      <h2>💾 持久化对比</h2>
      <div class="demo-grid">
        <div class="demo-card vue-card">
          <h3>Vue 3 手动持久化</h3>
          <input 
            v-model="vuePersistentData" 
            placeholder="输入数据（手动持久化）"
            class="demo-input"
          >
          <p class="note">需要手动处理 localStorage</p>
        </div>
        <div class="demo-card nuxt-card">
          <h3>Nuxt 3 结合持久化</h3>
          <input 
            v-model="nuxtPersistentData" 
            placeholder="输入数据（结合持久化）"
            class="demo-input"
          >
          <p class="note">可以结合 useState 实现</p>
        </div>
      </div>
    </section>

    <!-- 使用场景建议 -->
    <section class="recommendation-section">
      <h2>💡 使用场景建议</h2>
      <div class="recommendation-grid">
        <div class="recommendation-card vue-card">
          <h3>使用 Vue 3 ref/reactive 的场景</h3>
          <ul>
            <li>✅ 组件内部的临时状态</li>
            <li>✅ 表单数据处理</li>
            <li>✅ 组件间的父子通信</li>
            <li>✅ 不需要跨页面保持的状态</li>
            <li>✅ 计算密集型的响应式数据</li>
          </ul>
        </div>
        <div class="recommendation-card nuxt-card">
          <h3>使用 Nuxt 3 useState 的场景</h3>
          <ul>
            <li>✅ 用户认证状态</li>
            <li>✅ 全局主题、语言设置</li>
            <li>✅ 购物车、收藏夹</li>
            <li>✅ 需要跨页面保持的状态</li>
            <li>✅ 需要 SSR 同步的状态</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 导航 -->
    <div class="navigation">
      <NuxtLink to="/use-state">← 返回学习中心</NuxtLink>
      <NuxtLink to="/use-state/docs">📚 查看文档</NuxtLink>
      <NuxtLink to="/use-state/examples">📝 基础示例</NuxtLink>
    </div>
  </div>
</template>

<style lang="less" scoped>
.comparison-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 40px;
  font-size: 2.5em;
}

.concept-section, .demo-section, .recommendation-section {
  margin-bottom: 50px;

  h2 {
    color: #34495e;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
  }
}

.concept-grid, .demo-grid, .recommendation-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.concept-card, .demo-card, .recommendation-card {
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
  }

  h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.3em;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin: 10px 0;
      padding-left: 5px;
    }
  }
}

.vue-card {
  background: linear-gradient(135deg, #42b883 0%, #369870 100%);
  color: white;
  border-left: 5px solid #42b883;

  h3 {
    color: white;
  }
}

.nuxt-card {
  background: linear-gradient(135deg, #00dc82 0%, #00b86b 100%);
  color: white;
  border-left: 5px solid #00dc82;

  h3 {
    color: white;
  }
}

.demo-card {
  background: white;
  color: #2c3e50;

  button {
    margin: 5px 10px 5px 0;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .note {
    font-size: 0.9em;
    opacity: 0.7;
    font-style: italic;
    margin: 10px 0;
  }

  .theme-info {
    font-weight: bold;
    margin-top: 15px;
  }
}

.vue-card button {
  background: #42b883;
  color: white;

  &:hover {
    background: #369870;
  }
}

.nuxt-card button {
  background: #00dc82;
  color: white;

  &:hover {
    background: #00b86b;
  }
}

.demo-input {
  width: 100%;
  padding: 10px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  margin: 10px 0;

  &:focus {
    outline: none;
    border-color: #3498db;
  }
}

.navigation {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin: 50px 0;
  flex-wrap: wrap;

  a {
    padding: 12px 24px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;

    &:hover {
      background: #2980b9;
      transform: translateY(-2px);
    }
  }
}
</style>

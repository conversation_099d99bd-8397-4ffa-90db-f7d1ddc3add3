<script setup lang="ts">
useHead({
	title: "Rex's Nuxt title",
	link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
	meta: [{ name: 'description', content: '我的精彩网站。' }],
	bodyAttrs: {
		class: 'test',
	},
	htmlAttrs: {
		class: 'rex',
	},
	// script: [{ innerHTML: 'console.log("hello world")' }],
	script: [
		{
			src: 'https://third-party-script.com',
			// 有效选项为：'head' | 'bodyClose' | 'bodyOpen'
			tagPosition: 'bodyOpen',
		},
	],
	titleTemplate: '%s - 站点标题',
})
</script>

<template>
	<div>
		<NuxtLayout>
			<NuxtPage />
		</NuxtLayout>
	</div>
</template>

<style>
.page-enter-active,
.page-leave-active {
	transition: all 0.4s;
}
.page-enter-from,
.page-leave-to {
	opacity: 0;
	filter: blur(1rem);
}
</style>

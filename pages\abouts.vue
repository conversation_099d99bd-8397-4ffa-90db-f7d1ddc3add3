<script setup lang="ts">
definePageMeta({
	title: '某个页面',
})
useHead({
	title: 'about page',
})

const onFetch = async () => {
	await useFetch('/api/fetch', {
		query: {
			'name': '<PERSON>',
		},
		onRequest: async ({ request, options }) => {
      console.log('%c [ options ]-15', 'font-size:13px; background:#b724c3; color:#fb68ff;', options);
      console.log('%c [ request ]-15', 'font-size:13px; background:#1418dd; color:#585cff;', request);

      options.query = {
        ...options.query,
        age:18,
      }

      options.query.address='asdasdsa'
    },
	})
}
</script>

<template>
	<section>
		<p>此页面将在 /about 路由下显示。</p>
		<img src="/favicon.ico" alt="" srcset="" >
		<div>
			<nuxt-link to="/"> 回到首页 </nuxt-link>
			<button @click="onFetch">
        fetch
        </button>
		</div>
	</section>
</template>

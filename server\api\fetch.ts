export default defineEventHandler(async (event)=>{

  const query = await getValidatedQuery(event,(data)=>{
  console.log('%c [ data ]-4', 'font-size:13px; background:#5bc306; color:#9fff4a;', data);
    return 'age' in data 
  })
  // const query = getQuery(event)
  // const deleteKey = Object.keys(query)?.[0] as string
  // delete query[deleteKey]
  console.log('%c [ query ]-4', 'font-size:13px; background:#05eeec; color:#49ffff;', query);
  // return  new Error('报错了')

 
})
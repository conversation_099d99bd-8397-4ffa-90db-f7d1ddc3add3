<script setup lang="ts">
// 1. 基础用法 - 不带初始值
const counter = useState<number>('counter')

// 2. 带初始值的用法
const user = useState('user', () => ({
  name: '<PERSON>',
  age: 25,
  email: '<EMAIL>'
}))

// 3. 带类型定义的复杂状态
interface UserProfile {
  id: number
  name: string
  avatar?: string
  preferences: {
    theme: 'light' | 'dark'
    language: string
  }
}

const userProfile = useState<UserProfile>('userProfile', () => ({
  id: 1,
  name: 'Alice',
  preferences: {
    theme: 'light',
    language: 'zh-CN'
  }
}))

// 4. 数组状态
const todoList = useState<string[]>('todos', () => [
  '学习 Nuxt 3',
  '理解 useState',
  '构建应用'
])

// 5. 布尔状态
const isLoading = useState<boolean>('loading', () => false)
const isAuthenticated = useState<boolean>('auth', () => false)

// 6. 方法：操作状态
const increment = () => {
  if (counter.value === undefined) {
    counter.value = 0
  }
  counter.value++
}

const decrement = () => {
  if (counter.value === undefined) {
    counter.value = 0
  }
  counter.value--
}

const updateUser = (field: string, value: any) => {
  if (user.value) {
    user.value[field] = value
  }
}

const addTodo = (todo: string) => {
  if (todoList.value) {
    todoList.value.push(todo)
  }
}

const removeTodo = (index: number) => {
  if (todoList.value) {
    todoList.value.splice(index, 1)
  }
}

const toggleTheme = () => {
  if (userProfile.value) {
    userProfile.value.preferences.theme = 
      userProfile.value.preferences.theme === 'light' ? 'dark' : 'light'
  }
}

// 7. 计算属性基于 useState
const userDisplayName = computed(() => {
  return user.value ? `${user.value.name} (${user.value.age}岁)` : '未知用户'
})

const todoCount = computed(() => {
  return todoList.value ? todoList.value.length : 0
})

// 8. 监听状态变化
watch(counter, (newVal, oldVal) => {
  console.log(`计数器从 ${oldVal} 变为 ${newVal}`)
})

watch(() => userProfile.value?.preferences.theme, (newTheme) => {
  console.log(`主题切换为: ${newTheme}`)
  // 可以在这里应用主题到 document
  if (process.client) {
    document.documentElement.setAttribute('data-theme', newTheme || 'light')
  }
})

// 9. 新的 todo 输入
const newTodo = ref('')

const handleAddTodo = () => {
  if (newTodo.value.trim()) {
    addTodo(newTodo.value.trim())
    newTodo.value = ''
  }
}
</script>

<template>
  <div class="examples-container">
    <h1>useState 详细用法示例</h1>
    
    <!-- 1. 计数器示例 -->
    <section class="example-section">
      <h2>1. 基础计数器</h2>
      <div class="counter-demo">
        <p>当前计数: <strong>{{ counter ?? 0 }}</strong></p>
        <button @click="increment">+1</button>
        <button @click="decrement">-1</button>
      </div>
    </section>

    <!-- 2. 用户信息示例 -->
    <section class="example-section">
      <h2>2. 用户信息管理</h2>
      <div class="user-demo">
        <p>显示名称: {{ userDisplayName }}</p>
        <div class="form-group">
          <label>姓名:</label>
          <input 
            :value="user?.name || ''" 
            @input="updateUser('name', ($event.target as HTMLInputElement).value)"
            type="text"
          >
        </div>
        <div class="form-group">
          <label>年龄:</label>
          <input 
            :value="user?.age || ''" 
            @input="updateUser('age', parseInt(($event.target as HTMLInputElement).value) || 0)"
            type="number"
          >
        </div>
        <div class="form-group">
          <label>邮箱:</label>
          <input 
            :value="user?.email || ''" 
            @input="updateUser('email', ($event.target as HTMLInputElement).value)"
            type="email"
          >
        </div>
      </div>
    </section>

    <!-- 3. 用户配置示例 -->
    <section class="example-section">
      <h2>3. 用户配置</h2>
      <div class="profile-demo">
        <p>当前主题: <strong>{{ userProfile?.preferences.theme }}</strong></p>
        <button @click="toggleTheme">切换主题</button>
        <div class="profile-info">
          <p>用户ID: {{ userProfile?.id }}</p>
          <p>用户名: {{ userProfile?.name }}</p>
          <p>语言: {{ userProfile?.preferences.language }}</p>
        </div>
      </div>
    </section>

    <!-- 4. 待办事项示例 -->
    <section class="example-section">
      <h2>4. 待办事项列表</h2>
      <div class="todo-demo">
        <p>总计: {{ todoCount }} 项任务</p>
        <div class="add-todo">
          <input 
            v-model="newTodo" 
            @keyup.enter="handleAddTodo"
            placeholder="添加新任务..."
            type="text"
          >
          <button @click="handleAddTodo">添加</button>
        </div>
        <ul class="todo-list">
          <li v-for="(todo, index) in todoList" :key="index">
            {{ todo }}
            <button @click="removeTodo(index)">删除</button>
          </li>
        </ul>
      </div>
    </section>

    <!-- 5. 加载状态示例 -->
    <section class="example-section">
      <h2>5. 状态管理</h2>
      <div class="status-demo">
        <p>加载状态: {{ isLoading ? '加载中...' : '已完成' }}</p>
        <p>认证状态: {{ isAuthenticated ? '已登录' : '未登录' }}</p>
        <button @click="isLoading = !isLoading">切换加载状态</button>
        <button @click="isAuthenticated = !isAuthenticated">切换认证状态</button>
      </div>
    </section>

    <!-- 6. 状态调试信息 -->
    <section class="example-section">
      <h2>6. 状态调试信息</h2>
      <div class="debug-info">
        <details>
          <summary>查看所有状态</summary>
          <pre>{{ {
            counter: counter,
            user: user,
            userProfile: userProfile,
            todoList: todoList,
            isLoading: isLoading,
            isAuthenticated: isAuthenticated
          } }}</pre>
        </details>
      </div>
    </section>
  </div>
</template>

<style lang="less" scoped>
.examples-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.example-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #f8f9fa;

  h2 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
  }
}

.counter-demo {
  button {
    margin: 0 5px;
    padding: 8px 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: #2980b9;
    }
  }
}

.form-group {
  margin: 10px 0;
  display: flex;
  align-items: center;

  label {
    width: 60px;
    font-weight: bold;
  }

  input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-left: 10px;
  }
}

.profile-demo {
  button {
    padding: 8px 16px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 10px 0;

    &:hover {
      background: #c0392b;
    }
  }

  .profile-info {
    margin-top: 15px;
    padding: 10px;
    background: white;
    border-radius: 4px;
  }
}

.add-todo {
  display: flex;
  margin: 15px 0;

  input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
  }

  button {
    padding: 8px 16px;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;

    &:hover {
      background: #229954;
    }
  }
}

.todo-list {
  list-style: none;
  padding: 0;

  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background: white;
    border-radius: 4px;
    border: 1px solid #eee;

    button {
      padding: 4px 8px;
      background: #e74c3c;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;

      &:hover {
        background: #c0392b;
      }
    }
  }
}

.status-demo {
  button {
    margin: 5px 10px 5px 0;
    padding: 8px 16px;
    background: #f39c12;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: #e67e22;
    }
  }
}

.debug-info {
  details {
    summary {
      cursor: pointer;
      font-weight: bold;
      padding: 10px;
      background: #34495e;
      color: white;
      border-radius: 4px;
    }

    pre {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      margin-top: 10px;
    }
  }
}
</style>

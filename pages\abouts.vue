<script setup lang="ts">
definePageMeta({
	title: '某个页面',
})
useHead({
	title: 'about page',
})

const result =ref()

const onFetch = async () => {

  console.log('%c [  ]-11', 'font-size:13px; background:#2ce97f; color:#70ffc3;', '?');
const {data,status,error} = await useFetch('/api/fetch', {
  cache:'reload',
  immediate:true,
  // pick: ['age'],
  query: {
    'name': 'Rex2321123',
  },
  
  onRequest: async ({ request, options }) => {
    
    options.query = {
      ...options.query,
      age:18,
    }
    
    options.query.address='asdasdsa'
  },
})
result.value = data.value

console.log('%c [ status,error ]-11', 'font-size:13px; background:#732b07; color:#b76f4b;', status.value,error.value);

  console.log('%c [  ]-30', 'font-size:13px; background:#b7d07e; color:#fbffc2;', data.value);
}
onMounted(async ()=>{

  console.log('%c [ onMounted ]-40', 'font-size:13px; background:#580b9a; color:#9c4fde;', );
})
</script>

<template>
	<section>
    {{ result }}
		<p>此页面将在 /about 路由下显示。</p>
		<img src="/favicon.ico" alt="" srcset="" >
		<div>
			<nuxt-link to="/"> 回到首页 </nuxt-link>
			<button @click="onFetch">
        fetch
        </button>
		</div>
	</section>
</template>

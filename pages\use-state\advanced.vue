<script setup lang="ts">
// ==================== 高级 useState 用法示例 ====================

// 1. 使用 useState 创建全局状态管理
interface AppState {
  theme: 'light' | 'dark'
  language: 'zh' | 'en'
  user: {
    id: number
    name: string
    avatar?: string
  } | null
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    message: string
    timestamp: number
  }>
}

// 全局应用状态
const appState = useState<AppState>('app-state', () => ({
  theme: 'light',
  language: 'zh',
  user: null,
  notifications: []
}))

// 2. 创建状态管理的组合式函数
const useAppState = () => {
  // 主题管理
  const toggleTheme = () => {
    if (appState.value) {
      appState.value.theme = appState.value.theme === 'light' ? 'dark' : 'light'
    }
  }

  // 语言切换
  const setLanguage = (lang: 'zh' | 'en') => {
    if (appState.value) {
      appState.value.language = lang
    }
  }

  // 用户登录
  const login = (user: { id: number; name: string; avatar?: string }) => {
    if (appState.value) {
      appState.value.user = user
    }
  }

  // 用户登出
  const logout = () => {
    if (appState.value) {
      appState.value.user = null
    }
  }

  // 添加通知
  const addNotification = (
    type: 'success' | 'error' | 'warning' | 'info',
    message: string
  ) => {
    if (appState.value) {
      const notification = {
        id: Date.now().toString(),
        type,
        message,
        timestamp: Date.now()
      }
      appState.value.notifications.push(notification)
      
      // 3秒后自动移除通知
      setTimeout(() => {
        removeNotification(notification.id)
      }, 3000)
    }
  }

  // 移除通知
  const removeNotification = (id: string) => {
    if (appState.value) {
      const index = appState.value.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        appState.value.notifications.splice(index, 1)
      }
    }
  }

  return {
    appState: readonly(appState),
    toggleTheme,
    setLanguage,
    login,
    logout,
    addNotification,
    removeNotification
  }
}

// 3. 使用组合式函数
const {
  appState: state,
  toggleTheme,
  setLanguage,
  login,
  logout,
  addNotification,
  removeNotification
} = useAppState()

// 4. 购物车状态示例
interface CartItem {
  id: number
  name: string
  price: number
  quantity: number
  image?: string
}

const cart = useState<CartItem[]>('shopping-cart', () => [])

// 购物车操作
const addToCart = (item: Omit<CartItem, 'quantity'>) => {
  if (!cart.value) return
  
  const existingItem = cart.value.find(i => i.id === item.id)
  if (existingItem) {
    existingItem.quantity++
  } else {
    cart.value.push({ ...item, quantity: 1 })
  }
  addNotification('success', `${item.name} 已添加到购物车`)
}

const removeFromCart = (id: number) => {
  if (!cart.value) return
  
  const index = cart.value.findIndex(item => item.id === id)
  if (index > -1) {
    const item = cart.value[index]
    cart.value.splice(index, 1)
    addNotification('info', `${item.name} 已从购物车移除`)
  }
}

const updateQuantity = (id: number, quantity: number) => {
  if (!cart.value) return
  
  const item = cart.value.find(i => i.id === id)
  if (item) {
    if (quantity <= 0) {
      removeFromCart(id)
    } else {
      item.quantity = quantity
    }
  }
}

// 5. 计算属性
const cartTotal = computed(() => {
  if (!cart.value) return 0
  return cart.value.reduce((total, item) => total + item.price * item.quantity, 0)
})

const cartItemCount = computed(() => {
  if (!cart.value) return 0
  return cart.value.reduce((count, item) => count + item.quantity, 0)
})

const isLoggedIn = computed(() => {
  return state.value?.user !== null
})

// 6. 监听器
watch(() => state.value?.theme, (newTheme) => {
  if (import.meta.client && newTheme) {
    document.documentElement.setAttribute('data-theme', newTheme)
    localStorage.setItem('theme', newTheme)
  }
})

watch(() => state.value?.language, (newLang) => {
  if (import.meta.client && newLang) {
    localStorage.setItem('language', newLang)
  }
})

// 7. 生命周期 - 从 localStorage 恢复状态
onMounted(() => {
  if (import.meta.client) {
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark'
    const savedLang = localStorage.getItem('language') as 'zh' | 'en'
    
    if (savedTheme && state.value) {
      state.value.theme = savedTheme
    }
    if (savedLang && state.value) {
      state.value.language = savedLang
    }
  }
})

// 8. 示例数据
const sampleProducts = [
  { id: 1, name: 'MacBook Pro', price: 12999, image: '💻' },
  { id: 2, name: 'iPhone 15', price: 5999, image: '📱' },
  { id: 3, name: 'AirPods Pro', price: 1899, image: '🎧' },
  { id: 4, name: 'iPad Air', price: 4399, image: '📱' }
]

// 9. 表单数据
const loginForm = reactive({
  name: '',
  id: 0
})

const handleLogin = () => {
  if (loginForm.name && loginForm.id) {
    login({
      id: loginForm.id,
      name: loginForm.name,
      avatar: '👤'
    })
    addNotification('success', `欢迎回来，${loginForm.name}！`)
    loginForm.name = ''
    loginForm.id = 0
  }
}

const handleLogout = () => {
  logout()
  addNotification('info', '您已成功登出')
}


const todos = useState('todos')

</script>

<template>
  <div class="advanced-container" :data-theme="state?.theme">
    <h1>useState 高级用法示例</h1>

    <!-- 全局状态控制面板 -->
    <section class="control-panel">
      <h2>🎛️ 全局状态控制</h2>
      <div class="controls">
        <button @click="toggleTheme">
          切换主题 (当前: {{ state?.theme }})
        </button>
        <button @click="setLanguage(state?.language === 'zh' ? 'en' : 'zh')">
          切换语言 (当前: {{ state?.language }})
        </button>
      </div>
    </section>

    <!-- 用户认证 -->
    <section class="auth-section">
      <h2>👤 用户认证</h2>
      <div v-if="!isLoggedIn" class="login-form">
        <input v-model="loginForm.name" placeholder="用户名" >
        <input v-model.number="loginForm.id" placeholder="用户ID" type="number" >
        <button @click="handleLogin">登录</button>
      </div>
      <div v-else class="user-info">
        <p>欢迎，{{ state?.user?.name }}！ (ID: {{ state?.user?.id }})</p>
        <button @click="handleLogout">登出</button>
      </div>
    </section>

    <!-- 购物车 -->
    <section class="cart-section">
      <h2>🛒 购物车 ({{ cartItemCount }} 件商品)</h2>
      
      <!-- 商品列表 -->
      <div class="products">
        <h3>商品列表</h3>
        <div class="product-grid">
          <div v-for="product in sampleProducts" :key="product.id" class="product-card">
            <div class="product-icon">{{ product.image }}</div>
            <h4>{{ product.name }}</h4>
            <p class="price">¥{{ product.price }}</p>
            <button @click="addToCart(product)">加入购物车</button>
          </div>
        </div>
      </div>

      <!-- 购物车内容 -->
      <div v-if="cart && cart.length > 0" class="cart-content">
        <h3>购物车内容</h3>
        <div class="cart-items">
          <div v-for="item in cart" :key="item.id" class="cart-item">
            <span class="item-icon">{{ sampleProducts.find(p => p.id === item.id)?.image }}</span>
            <span class="item-name">{{ item.name }}</span>
            <span class="item-price">¥{{ item.price }}</span>
            <div class="quantity-controls">
              <button @click="updateQuantity(item.id, item.quantity - 1)">-</button>
              <span>{{ item.quantity }}</span>
              <button @click="updateQuantity(item.id, item.quantity + 1)">+</button>
            </div>
            <button class="remove-btn" @click="removeFromCart(item.id)">删除</button>
          </div>
        </div>
        <div class="cart-total">
          <strong>总计: ¥{{ cartTotal }}</strong>
        </div>
      </div>
    </section>

    <!-- 通知系统 -->
    <div class="notifications">
      <div
        v-for="notification in state?.notifications"
        :key="notification.id"
        :class="['notification', notification.type]"
        @click="removeNotification(notification.id)"
      >
        {{ notification.message }}
        <span class="close">×</span>
      </div>
    </div>

    <!-- 状态调试 -->
    <section class="debug-section">
      <h2>🔍 状态调试</h2>
      <details>
        <summary>查看完整状态</summary>
        <pre>{{ {
          appState: state,
          cart: cart,
          cartTotal: cartTotal,
          cartItemCount: cartItemCount,
          isLoggedIn: isLoggedIn
        } }}</pre>
      </details>
    </section>
  </div>
</template>

<style lang="less" scoped>
.advanced-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  &[data-theme="dark"] {
    background: #2c3e50;
    color: #ecf0f1;

    section {
      background: #34495e;
      border-color: #4a5f7a;
    }

    .product-card, .cart-item {
      background: #3a4a5c;
      border-color: #4a5f7a;
    }

    input {
      background: #3a4a5c;
      color: #ecf0f1;
      border-color: #4a5f7a;
    }
  }
}

section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;

  h2 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
  }
}

.controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

button {
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;

  &:hover {
    background: #2980b9;
  }

  &.remove-btn {
    background: #e74c3c;
    &:hover {
      background: #c0392b;
    }
  }
}

.login-form {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;

  input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.product-card {
  padding: 15px;
  background: white;
  border: 1px solid #eee;
  border-radius: 8px;
  text-align: center;

  .product-icon {
    font-size: 2em;
    margin-bottom: 10px;
  }

  h4 {
    margin: 10px 0;
  }

  .price {
    font-weight: bold;
    color: #e74c3c;
    margin: 10px 0;
  }
}

.cart-items {
  margin-top: 15px;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  background: white;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;

  .item-icon {
    font-size: 1.5em;
  }

  .item-name {
    flex: 1;
  }

  .quantity-controls {
    display: flex;
    align-items: center;
    gap: 5px;

    button {
      width: 30px;
      height: 30px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.cart-total {
  text-align: right;
  margin-top: 15px;
  font-size: 1.2em;
}

.notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.notification {
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 250px;
  animation: slideIn 0.3s ease;

  &.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  &.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }

  &.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
  }

  .close {
    font-weight: bold;
    margin-left: 10px;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.debug-section {
  details {
    summary {
      cursor: pointer;
      font-weight: bold;
      padding: 10px;
      background: #34495e;
      color: white;
      border-radius: 4px;
    }

    pre {
      background: #2c3e50;
      color: #ecf0f1;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      margin-top: 10px;
      font-size: 12px;
    }
  }
}
</style>

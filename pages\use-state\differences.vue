<script setup lang="ts">
definePageMeta({
  layout: 'empty',
})

useHead({
  title: 'useState vs ref/reactive - 详细对比',
  meta: [
    { name: 'description', content: 'Nuxt 3 useState 与 Vue 3 ref/reactive 的详细对比分析' }
  ]
})

// 对比数据
const comparisonData = [
  {
    aspect: '作用域',
    vue3: '组件级别',
    nuxt3: '应用级别（全局）',
    description: 'Vue 3 的状态仅在组件内有效，useState 在整个应用中共享'
  },
  {
    aspect: '生命周期',
    vue3: '随组件创建/销毁',
    nuxt3: '应用生命周期内持续',
    description: 'Vue 3 状态在组件销毁时消失，useState 在应用运行期间一直存在'
  },
  {
    aspect: 'SSR 兼容性',
    vue3: '需要手动处理',
    nuxt3: '自动处理',
    description: 'useState 自动解决服务端渲染和客户端水合的状态同步问题'
  },
  {
    aspect: '状态共享',
    vue3: '需要 provide/inject 或状态管理库',
    nuxt3: '相同 key 自动共享',
    description: 'useState 通过 key 实现跨组件状态共享，无需额外配置'
  },
  {
    aspect: '持久化',
    vue3: '需要手动实现',
    nuxt3: '可结合其他工具实现',
    description: '两者都需要额外工作来实现持久化，但 useState 更容易集成'
  },
  {
    aspect: '性能',
    vue3: '组件级别优化',
    nuxt3: '全局状态，需要谨慎使用',
    description: 'ref/reactive 在组件内性能更好，useState 适合真正需要全局的状态'
  }
]

// 使用场景对比
const useCases = {
  vue3: [
    {
      title: '表单数据处理',
      description: '处理表单输入、验证等临时数据',
      example: 'const formData = ref({ name: "", email: "" })'
    },
    {
      title: '组件内部状态',
      description: '模态框显示/隐藏、加载状态等',
      example: 'const isVisible = ref(false)'
    },
    {
      title: '计算密集型数据',
      description: '需要频繁计算的响应式数据',
      example: 'const expensiveComputed = computed(() => heavyCalculation())'
    },
    {
      title: '父子组件通信',
      description: '通过 props 和 emit 进行的数据传递',
      example: 'const childData = ref(props.initialValue)'
    }
  ],
  nuxt3: [
    {
      title: '用户认证状态',
      description: '登录状态、用户信息等需要全局访问的数据',
      example: 'const user = useState("user", () => null)'
    },
    {
      title: '全局配置',
      description: '主题、语言、应用设置等',
      example: 'const theme = useState("theme", () => "light")'
    },
    {
      title: '购物车数据',
      description: '需要在多个页面间保持的用户数据',
      example: 'const cart = useState("cart", () => [])'
    },
    {
      title: '应用级 UI 状态',
      description: '侧边栏状态、通知等全局 UI 状态',
      example: 'const notifications = useState("notifications", () => [])'
    }
  ]
}

// 最佳实践
const bestPractices = [
  {
    title: '选择原则',
    vue3: '优先使用 ref/reactive',
    nuxt3: '只在真正需要全局状态时使用',
    tip: '不要为了方便而过度使用 useState，这可能导致状态管理混乱'
  },
  {
    title: '命名规范',
    vue3: '使用描述性的变量名',
    nuxt3: '使用唯一且有意义的 key',
    tip: 'useState 的 key 应该是全局唯一的，避免冲突'
  },
  {
    title: '类型安全',
    vue3: '使用 TypeScript 泛型',
    nuxt3: '定义明确的接口类型',
    tip: '两者都应该充分利用 TypeScript 的类型系统'
  },
  {
    title: '性能考虑',
    vue3: '合理使用 computed 和 watch',
    nuxt3: '避免在 useState 中存储大量数据',
    tip: '大量数据或频繁变化的数据更适合使用专门的状态管理库'
  }
]
</script>

<template>
  <div class="differences-container">
    <header class="header">
      <h1>🔍 useState vs ref/reactive 详细对比</h1>
      <p class="subtitle">深入理解 Nuxt 3 和 Vue 3 状态管理的区别与选择</p>
    </header>

    <!-- 核心区别对比表 -->
    <section class="comparison-table-section">
      <h2>📊 核心区别对比</h2>
      <div class="table-container">
        <table class="comparison-table">
          <thead>
            <tr>
              <th>对比维度</th>
              <th class="vue-column">Vue 3 ref/reactive</th>
              <th class="nuxt-column">Nuxt 3 useState</th>
              <th>说明</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in comparisonData" :key="item.aspect">
              <td class="aspect-cell">{{ item.aspect }}</td>
              <td class="vue-cell">{{ item.vue3 }}</td>
              <td class="nuxt-cell">{{ item.nuxt3 }}</td>
              <td class="description-cell">{{ item.description }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>

    <!-- 使用场景对比 -->
    <section class="use-cases-section">
      <h2>🎯 使用场景对比</h2>
      <div class="use-cases-grid">
        <div class="use-case-column vue-column">
          <h3>Vue 3 ref/reactive 适用场景</h3>
          <div class="use-case-list">
            <div v-for="useCase in useCases.vue3" :key="useCase.title" class="use-case-card">
              <h4>{{ useCase.title }}</h4>
              <p>{{ useCase.description }}</p>
              <code>{{ useCase.example }}</code>
            </div>
          </div>
        </div>
        <div class="use-case-column nuxt-column">
          <h3>Nuxt 3 useState 适用场景</h3>
          <div class="use-case-list">
            <div v-for="useCase in useCases.nuxt3" :key="useCase.title" class="use-case-card">
              <h4>{{ useCase.title }}</h4>
              <p>{{ useCase.description }}</p>
              <code>{{ useCase.example }}</code>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 最佳实践 -->
    <section class="best-practices-section">
      <h2>✅ 最佳实践建议</h2>
      <div class="practices-grid">
        <div v-for="practice in bestPractices" :key="practice.title" class="practice-card">
          <h3>{{ practice.title }}</h3>
          <div class="practice-content">
            <div class="practice-item vue-practice">
              <h4>Vue 3</h4>
              <p>{{ practice.vue3 }}</p>
            </div>
            <div class="practice-item nuxt-practice">
              <h4>Nuxt 3</h4>
              <p>{{ practice.nuxt3 }}</p>
            </div>
          </div>
          <div class="practice-tip">
            <strong>💡 提示：</strong> {{ practice.tip }}
          </div>
        </div>
      </div>
    </section>

    <!-- 代码示例对比 -->
    <section class="code-examples-section">
      <h2>💻 代码示例对比</h2>
      
      <div class="example-grid">
        <div class="example-card vue-example">
          <h3>Vue 3 示例</h3>
          <pre><code>// 组件内状态
const count = ref(0)
const user = reactive({
  name: 'John',
  age: 25
})

// 计算属性
const displayName = computed(() => {
  return `${user.name} (${user.age}岁)`
})

// 监听变化
watch(count, (newVal) => {
  console.log('Count changed:', newVal)
})

// 方法
const increment = () => {
  count.value++
}</code></pre>
        </div>

        <div class="example-card nuxt-example">
          <h3>Nuxt 3 示例</h3>
          <pre><code>// 全局状态
const count = useState('count', () => 0)
const user = useState('user', () => ({
  name: 'John',
  age: 25
}))

// 计算属性
const displayName = computed(() => {
  if (!user.value) return ''
  return `${user.value.name} (${user.value.age}岁)`
})

// 监听变化
watch(count, (newVal) => {
  console.log('Global count changed:', newVal)
})

// 方法
const increment = () => {
  count.value++
}</code></pre>
        </div>
      </div>
    </section>

    <!-- 决策流程图 -->
    <section class="decision-section">
      <h2>🤔 如何选择？</h2>
      <div class="decision-flow">
        <div class="decision-step">
          <div class="question">这个状态需要在多个组件间共享吗？</div>
          <div class="decision-branches">
            <div class="branch">
              <div class="answer no">否</div>
              <div class="recommendation">使用 Vue 3 ref/reactive</div>
            </div>
            <div class="branch">
              <div class="answer yes">是</div>
              <div class="sub-question">需要跨页面保持状态吗？</div>
              <div class="sub-branches">
                <div class="sub-branch">
                  <div class="answer no">否</div>
                  <div class="recommendation">考虑 provide/inject</div>
                </div>
                <div class="sub-branch">
                  <div class="answer yes">是</div>
                  <div class="recommendation">使用 Nuxt 3 useState</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 总结 -->
    <section class="summary-section">
      <h2>📝 总结</h2>
      <div class="summary-content">
        <div class="summary-card">
          <h3>🎯 核心原则</h3>
          <ul>
            <li><strong>就近原则</strong>：优先使用组件级别的 ref/reactive</li>
            <li><strong>按需全局</strong>：只在真正需要全局状态时使用 useState</li>
            <li><strong>类型安全</strong>：充分利用 TypeScript 的类型系统</li>
            <li><strong>性能考虑</strong>：避免在全局状态中存储大量或频繁变化的数据</li>
          </ul>
        </div>
        
        <div class="summary-card">
          <h3>🚀 实际应用建议</h3>
          <ul>
            <li>表单处理、UI 状态 → <strong>ref/reactive</strong></li>
            <li>用户认证、全局配置 → <strong>useState</strong></li>
            <li>复杂状态管理 → 考虑 <strong>Pinia</strong> 等专门的状态管理库</li>
            <li>临时数据、计算结果 → <strong>ref/reactive</strong></li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 导航 -->
    <div class="navigation">
      <NuxtLink to="/use-state">← 返回学习中心</NuxtLink>
      <NuxtLink to="/use-state/comparison">🔍 交互式对比</NuxtLink>
      <NuxtLink to="/use-state/docs">📚 查看文档</NuxtLink>
    </div>
  </div>
</template>

<style lang="less" scoped>
.differences-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.header {
  text-align: center;
  margin-bottom: 50px;

  h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
  }

  .subtitle {
    color: #666;
    font-size: 1.2em;
    margin: 0;
  }
}

section {
  margin-bottom: 60px;

  h2 {
    color: #34495e;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
    font-size: 1.8em;
  }
}

// 对比表格样式
.table-container {
  overflow-x: auto;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-radius: 8px;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  background: white;

  th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
  }

  th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
  }

  .vue-column, .vue-cell {
    background: rgba(66, 184, 131, 0.1);
    border-left: 4px solid #42b883;
  }

  .nuxt-column, .nuxt-cell {
    background: rgba(0, 220, 130, 0.1);
    border-left: 4px solid #00dc82;
  }

  .aspect-cell {
    font-weight: 600;
    color: #2c3e50;
  }

  .description-cell {
    color: #666;
    font-size: 0.9em;
  }
}

// 使用场景样式
.use-cases-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.use-case-column {
  h3 {
    margin-top: 0;
    padding: 15px;
    border-radius: 8px 8px 0 0;
    color: white;
    text-align: center;
  }

  &.vue-column h3 {
    background: #42b883;
  }

  &.nuxt-column h3 {
    background: #00dc82;
  }
}

.use-case-list {
  border: 2px solid #e1e5e9;
  border-radius: 0 0 8px 8px;
  border-top: none;
}

.use-case-card {
  padding: 20px;
  border-bottom: 1px solid #e1e5e9;

  &:last-child {
    border-bottom: none;
  }

  h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 0.9em;
  }

  code {
    display: block;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 0.8em;
    color: #e74c3c;
    font-family: 'Monaco', 'Consolas', monospace;
  }
}

// 最佳实践样式
.practices-grid {
  display: grid;
  gap: 25px;
}

.practice-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);

  h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
  }

  .practice-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .practice-item {
    padding: 15px;
    border-radius: 6px;

    h4 {
      margin: 0 0 10px 0;
      color: white;
    }

    p {
      margin: 0;
      color: white;
      font-size: 0.9em;
    }

    &.vue-practice {
      background: #42b883;
    }

    &.nuxt-practice {
      background: #00dc82;
    }
  }

  .practice-tip {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    color: #856404;
    font-size: 0.9em;
  }
}

// 代码示例样式
.example-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.example-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);

  h3 {
    margin: 0;
    padding: 15px;
    color: white;
    text-align: center;
  }

  &.vue-example h3 {
    background: #42b883;
  }

  &.nuxt-example h3 {
    background: #00dc82;
  }

  pre {
    margin: 0;
    background: #2c3e50;
    color: #ecf0f1;
    padding: 20px;
    overflow-x: auto;

    code {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

// 决策流程样式
.decision-flow {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.decision-step {
  .question {
    background: #3498db;
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .decision-branches {
    display: flex;
    gap: 30px;
    justify-content: center;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .branch {
    text-align: center;
    flex: 1;
  }

  .answer {
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    display: inline-block;

    &.yes {
      background: #27ae60;
      color: white;
    }

    &.no {
      background: #e74c3c;
      color: white;
    }
  }

  .recommendation {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #3498db;
    font-weight: 600;
    color: #2c3e50;
  }

  .sub-question {
    background: #9b59b6;
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 0.9em;
    margin: 15px 0;
  }

  .sub-branches {
    display: flex;
    gap: 15px;
    justify-content: center;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .sub-branch {
    flex: 1;
  }
}

// 总结样式
.summary-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.summary-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);

  h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin: 12px 0;
      padding-left: 20px;
      position: relative;

      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #27ae60;
        font-weight: bold;
      }

      strong {
        color: #2c3e50;
      }
    }
  }
}

// 导航样式
.navigation {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin: 50px 0;
  flex-wrap: wrap;

  a {
    padding: 12px 24px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;

    &:hover {
      background: #2980b9;
      transform: translateY(-2px);
    }
  }
}
</style>

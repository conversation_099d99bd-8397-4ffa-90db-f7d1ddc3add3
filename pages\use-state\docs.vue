<script setup lang="ts">
// 设置页面元数据
useHead({
  title: 'useState 详细文档 - Nuxt 3',
  meta: [
    { name: 'description', content: 'Nuxt 3 useState 的详细使用文档和最佳实践' }
  ]
})

// 示例代码字符串
const basicExample = `// 基础用法
const counter = useState<number>('counter')

// 带初始值
const user = useState('user', () => ({
  name: '<PERSON>',
  age: 25
}))

// 操作状态
const increment = () => {
  if (counter.value === undefined) {
    counter.value = 0
  }
  counter.value++
}`

const typeExample = `// TypeScript 类型定义
interface UserProfile {
  id: number
  name: string
  preferences: {
    theme: 'light' | 'dark'
    language: string
  }
}

const userProfile = useState<UserProfile>('userProfile', () => ({
  id: 1,
  name: 'Alice',
  preferences: {
    theme: 'light',
    language: 'zh-CN'
  }
}))`

const composableExample = `// 创建可复用的状态管理组合式函数
const useCounter = () => {
  const count = useState<number>('counter', () => 0)
  
  const increment = () => count.value++
  const decrement = () => count.value--
  const reset = () => count.value = 0
  
  return {
    count: readonly(count),
    increment,
    decrement,
    reset
  }
}

// 在组件中使用
const { count, increment, decrement, reset } = useCounter()`

const persistExample = `// 持久化状态到 localStorage
const usePersistedState = <T>(key: string, defaultValue: T) => {
  const state = useState<T>(key, () => {
    if (process.client) {
      const saved = localStorage.getItem(key)
      return saved ? JSON.parse(saved) : defaultValue
    }
    return defaultValue
  })

  watch(state, (newValue) => {
    if (process.client) {
      localStorage.setItem(key, JSON.stringify(newValue))
    }
  }, { deep: true })

  return state
}`

const bestPractices = [
  {
    title: '使用有意义的 key',
    description: '为每个状态使用唯一且有意义的 key，避免冲突',
    example: `// ✅ 好的做法
const userProfile = useState('user-profile', () => ({}))
const shoppingCart = useState('shopping-cart', () => [])

// ❌ 避免的做法
const data1 = useState('data', () => ({}))
const data2 = useState('data', () => []) // key 冲突！`
  },
  {
    title: '提供初始值',
    description: '总是为 useState 提供初始值，避免 undefined 状态',
    example: `// ✅ 好的做法
const counter = useState('counter', () => 0)
const items = useState('items', () => [])

// ❌ 避免的做法
const counter = useState('counter') // 可能是 undefined`
  },
  {
    title: '使用 TypeScript 类型',
    description: '为复杂状态定义明确的 TypeScript 接口',
    example: `// ✅ 好的做法
interface AppState {
  theme: 'light' | 'dark'
  user: User | null
}
const appState = useState<AppState>('app-state', () => ({
  theme: 'light',
  user: null
}))`
  },
  {
    title: '创建组合式函数',
    description: '将相关的状态和操作封装到组合式函数中',
    example: `// ✅ 好的做法
const useAuth = () => {
  const user = useState('user', () => null)
  const login = (userData) => { user.value = userData }
  const logout = () => { user.value = null }
  return { user: readonly(user), login, logout }
}`
  }
]

const commonPitfalls = [
  {
    title: '直接修改响应式对象',
    problem: '直接修改嵌套对象可能不会触发响应式更新',
    solution: '使用 reactive() 或确保正确的响应式更新',
    example: `// ❌ 问题
const user = useState('user', () => ({ profile: {} }))
user.value.profile.name = 'John' // 可能不会触发更新

// ✅ 解决方案
const user = useState('user', () => reactive({ profile: {} }))
user.value.profile.name = 'John' // 正确触发更新`
  },
  {
    title: 'SSR 和客户端状态不一致',
    problem: '服务端和客户端的初始状态不同导致水合错误',
    solution: '确保初始状态在服务端和客户端保持一致',
    example: `// ❌ 问题
const theme = useState('theme', () => {
  return localStorage.getItem('theme') || 'light' // SSR 时 localStorage 不存在
})

// ✅ 解决方案
const theme = useState('theme', () => 'light')
onMounted(() => {
  if (process.client) {
    const saved = localStorage.getItem('theme')
    if (saved) theme.value = saved
  }
})`
  }
]
</script>

<template>
  <div class="docs-container">
    <header class="docs-header">
      <h1>📚 Nuxt 3 useState 详细文档</h1>
      <p class="subtitle">全面了解 Nuxt 3 中的状态管理利器</p>
    </header>

    <!-- 目录 -->
    <nav class="table-of-contents">
      <h2>📋 目录</h2>
      <ul>
        <li><a href="#overview">概述</a></li>
        <li><a href="#basic-usage">基础用法</a></li>
        <li><a href="#typescript">TypeScript 支持</a></li>
        <li><a href="#composables">组合式函数</a></li>
        <li><a href="#persistence">状态持久化</a></li>
        <li><a href="#best-practices">最佳实践</a></li>
        <li><a href="#common-pitfalls">常见陷阱</a></li>
        <li><a href="#examples">实际示例</a></li>
      </ul>
    </nav>

    <!-- 概述 -->
    <section id="overview" class="docs-section">
      <h2>🎯 概述</h2>
      <div class="content">
        <p><code>useState</code> 是 Nuxt 3 提供的一个强大的状态管理工具，它解决了以下核心问题：</p>
        <ul>
          <li><strong>SSR 友好</strong>：自动处理服务端渲染和客户端水合之间的状态同步</li>
          <li><strong>全局共享</strong>：相同 key 的状态在整个应用中共享</li>
          <li><strong>响应式</strong>：基于 Vue 3 的响应式系统</li>
          <li><strong>类型安全</strong>：完整的 TypeScript 支持</li>
        </ul>
        
        <div class="syntax-box">
          <h3>基本语法</h3>
          <pre><code>const state = useState&lt;T&gt;(key?: string, init?: () => T | Ref&lt;T&gt;)</code></pre>
        </div>
      </div>
    </section>

    <!-- 基础用法 -->
    <section id="basic-usage" class="docs-section">
      <h2>🚀 基础用法</h2>
      <div class="content">
        <div class="code-example">
          <h3>基础示例</h3>
          <pre><code>{{ basicExample }}</code></pre>
        </div>
        
        <div class="feature-grid">
          <div class="feature-card">
            <h4>🔑 唯一标识</h4>
            <p>每个状态需要一个唯一的 key 来标识，相同 key 的状态会在全局共享</p>
          </div>
          <div class="feature-card">
            <h4>🎯 初始值</h4>
            <p>通过初始化函数提供默认值，函数只在首次创建时执行</p>
          </div>
          <div class="feature-card">
            <h4>📡 响应式</h4>
            <p>返回的是 Ref 对象，可以直接在模板中使用或通过 .value 访问</p>
          </div>
        </div>
      </div>
    </section>

    <!-- TypeScript 支持 -->
    <section id="typescript" class="docs-section">
      <h2>🔷 TypeScript 支持</h2>
      <div class="content">
        <p>useState 提供完整的 TypeScript 支持，可以为状态定义精确的类型：</p>
        <div class="code-example">
          <pre><code>{{ typeExample }}</code></pre>
        </div>
      </div>
    </section>

    <!-- 组合式函数 -->
    <section id="composables" class="docs-section">
      <h2>🔧 组合式函数</h2>
      <div class="content">
        <p>将相关的状态和操作封装到可复用的组合式函数中：</p>
        <div class="code-example">
          <pre><code>{{ composableExample }}</code></pre>
        </div>
      </div>
    </section>

    <!-- 状态持久化 -->
    <section id="persistence" class="docs-section">
      <h2>💾 状态持久化</h2>
      <div class="content">
        <p>结合 localStorage 实现状态持久化：</p>
        <div class="code-example">
          <pre><code>{{ persistExample }}</code></pre>
        </div>
      </div>
    </section>

    <!-- 最佳实践 -->
    <section id="best-practices" class="docs-section">
      <h2>✅ 最佳实践</h2>
      <div class="content">
        <div class="practice-grid">
          <div v-for="practice in bestPractices" :key="practice.title" class="practice-card">
            <h3>{{ practice.title }}</h3>
            <p>{{ practice.description }}</p>
            <div class="code-example">
              <pre><code>{{ practice.example }}</code></pre>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 常见陷阱 -->
    <section id="common-pitfalls" class="docs-section">
      <h2>⚠️ 常见陷阱</h2>
      <div class="content">
        <div class="pitfall-grid">
          <div v-for="pitfall in commonPitfalls" :key="pitfall.title" class="pitfall-card">
            <h3>{{ pitfall.title }}</h3>
            <div class="problem">
              <h4>问题：</h4>
              <p>{{ pitfall.problem }}</p>
            </div>
            <div class="solution">
              <h4>解决方案：</h4>
              <p>{{ pitfall.solution }}</p>
            </div>
            <div class="code-example">
              <pre><code>{{ pitfall.example }}</code></pre>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 实际示例 -->
    <section id="examples" class="docs-section">
      <h2>🎨 实际示例</h2>
      <div class="content">
        <div class="example-links">
          <NuxtLink to="/use-state/examples" class="example-link">
            <h3>📝 基础示例</h3>
            <p>查看 useState 的基础用法示例</p>
          </NuxtLink>
          <NuxtLink to="/use-state/advanced" class="example-link">
            <h3>🚀 高级示例</h3>
            <p>探索 useState 的高级用法和最佳实践</p>
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- 总结 -->
    <section class="docs-section">
      <h2>📝 总结</h2>
      <div class="content">
        <div class="summary-box">
          <h3>useState 的核心优势：</h3>
          <ul>
            <li>🔄 <strong>SSR 兼容</strong>：自动处理服务端和客户端状态同步</li>
            <li>🌐 <strong>全局共享</strong>：跨组件、跨页面的状态共享</li>
            <li>⚡ <strong>响应式</strong>：基于 Vue 3 响应式系统</li>
            <li>🛡️ <strong>类型安全</strong>：完整的 TypeScript 支持</li>
            <li>🎯 <strong>简单易用</strong>：API 简洁，学习成本低</li>
          </ul>
          
          <h3>适用场景：</h3>
          <ul>
            <li>用户认证状态管理</li>
            <li>主题、语言等全局配置</li>
            <li>购物车、收藏夹等用户数据</li>
            <li>应用级别的 UI 状态</li>
            <li>需要在页面间保持的临时数据</li>
          </ul>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="less" scoped>
.docs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.docs-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;

  h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
  }

  .subtitle {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
  }
}

.table-of-contents {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;

  h2 {
    margin-top: 0;
    color: #2c3e50;
  }

  ul {
    list-style: none;
    padding: 0;

    li {
      margin: 8px 0;

      a {
        color: #3498db;
        text-decoration: none;
        padding: 5px 10px;
        border-radius: 4px;
        transition: background 0.3s;

        &:hover {
          background: #e3f2fd;
        }
      }
    }
  }
}

.docs-section {
  margin-bottom: 40px;
  padding: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);

  h2 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-top: 0;
  }

  h3 {
    color: #34495e;
    margin-top: 25px;
  }
}

.syntax-box {
  background: #f1f3f4;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #3498db;
  margin: 20px 0;

  h3 {
    margin-top: 0;
    color: #2c3e50;
  }

  pre {
    margin: 10px 0 0 0;
    background: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 4px;
    overflow-x: auto;
  }
}

.code-example {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin: 20px 0;

  h3 {
    margin: 0;
    padding: 15px 20px;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
  }

  pre {
    margin: 0;
    padding: 20px;
    background: #2c3e50;
    color: #ecf0f1;
    border-radius: 0 0 8px 8px;
    overflow-x: auto;

    code {
      font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.feature-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;

  h4 {
    margin-top: 0;
    color: #2c3e50;
  }
}

.practice-grid, .pitfall-grid {
  display: grid;
  gap: 25px;
  margin: 20px 0;
}

.practice-card, .pitfall-card {
  padding: 25px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h3 {
    margin-top: 0;
    color: #2c3e50;
  }
}

.pitfall-card {
  .problem {
    background: #fff5f5;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #e74c3c;
    margin: 15px 0;

    h4 {
      margin-top: 0;
      color: #c0392b;
    }
  }

  .solution {
    background: #f0fff4;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #27ae60;
    margin: 15px 0;

    h4 {
      margin-top: 0;
      color: #229954;
    }
  }
}

.example-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.example-link {
  display: block;
  padding: 25px;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }

  h3 {
    margin-top: 0;
    margin-bottom: 10px;
  }

  p {
    margin: 0;
    opacity: 0.9;
  }
}

.summary-box {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  padding: 30px;
  border-radius: 12px;
  margin: 20px 0;

  h3 {
    color: #2c3e50;
    margin-top: 0;
  }

  ul {
    margin: 15px 0;

    li {
      margin: 8px 0;
      padding-left: 10px;
    }
  }
}
</style>

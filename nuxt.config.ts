export default defineNuxtConfig({
    compatibilityDate: '2025-05-15',
    devtools: { enabled: true },
    modules: ['@nuxt/eslint'],
    css: ['~/assets/css/main.css'],
    // 内置的postcss
    postcss: {
        plugins: {
            'postcss-nested': {},
            'postcss-custom-media': {}
        }
    },

    devServer: {
        port: Number(process.env.SERVER_PORT)  || 8080,
    },

    app: {
        baseURL: process.env.PUBLIC_PATH || '/',
        head: {
            // title: 'Rex\'s Nuxt',
            htmlAttrs: {
                lang: 'en',
            },
            link: [
                { rel: 'icon', type: 'image/png', href: '/image.png' }
            ],
            charset: 'utf-16',
            viewport: 'width=device-width, initial-scale=2, maximum-scale=1',

        },
        pageTransition: { name: 'page', mode: 'out-in' }
    },

    routeRules: {
        // 为 SEO 目的在构建时生成
        '/': { prerender: true },
        // 缓存 1 小时
        '/api/*': { cache: { maxAge: 60 * 60 } },
        // 重定向以避免 404
        '/old-page': {
            redirect: { to: '/api', statusCode: 302 }
        }
        // ...
    }

})
<script setup lang="ts">
definePageMeta({
  layout: 'empty',
})

useHead({
  title: 'useFetch 详细指南 - Nuxt 3',
  meta: [
    { name: 'description', content: 'Nuxt 3 useFetch 和相关 fetch API 的完整学习指南' }
  ]
})

// ==================== 基础 useFetch 示例 ====================

// 1. 基础用法
const { data: basicData, pending: basicPending, error: basicError } = await useFetch('/api/basic')

// 2. 带参数的请求
const userId = ref('1')
const { data: userData, pending: userPending, refresh: refreshUser } = await useFetch(`/api/users/${userId.value}`)

// 3. POST 请求
const { data: postData, pending: postPending, execute: executePost } = await useFetch('/api/posts', {
  method: 'POST',
  body: {
    title: 'New Post',
    content: 'Post content'
  },
  immediate: false // 不立即执行
})

// 4. 响应式参数
const searchQuery = ref('')
const { data: searchResults, pending: searchPending } = await useFetch('/api/search', {
  query: {
    q: searchQuery
  },
  watch: [searchQuery] // 监听参数变化
})

// ==================== useLazyFetch 示例 ====================

// 5. 懒加载数据
const { data: lazyData, pending: lazyPending } = await useLazyFetch('/api/lazy-data')

// ==================== $fetch 示例 ====================

// 6. 手动 fetch
const manualFetchData = ref(null)
const manualFetching = ref(false)

const handleManualFetch = async () => {
  manualFetching.value = true
  try {
    manualFetchData.value = await $fetch('/api/manual')
  } catch (error) {
    console.error('Manual fetch error:', error)
  } finally {
    manualFetching.value = false
  }
}

// ==================== 高级用法示例 ====================

// 7. 带缓存的请求
const { data: cachedData } = await useFetch('/api/cached', {
  key: 'cached-data',
  server: true,
  default: () => ({ message: 'Default data' })
})

// 8. 条件请求
const shouldFetch = ref(false)
const { data: conditionalData } = await useFetch('/api/conditional', {
  server: false,
  lazy: true,
  immediate: shouldFetch.value
})

// 9. 错误处理
const { data: errorData, error: fetchError, status } = await useFetch('/api/might-fail', {
  onResponseError({ request, response, options }) {
    console.log('Response error:', response.status)
  },
  onRequestError({ request, options, error }) {
    console.log('Request error:', error)
  }
})

// ==================== 实际应用示例 ====================

// 10. 用户管理
interface User {
  id: number
  name: string
  email: string
  avatar?: string
}

const selectedUserId = ref<number>(1)
const { data: currentUser, pending: userLoading, refresh: reloadUser } = await useFetch<User>(`/api/users/${selectedUserId.value}`, {
  key: `user-${selectedUserId.value}`,
  watch: [selectedUserId]
})

// 11. 分页数据
const currentPage = ref(1)
const pageSize = ref(10)

const { data: paginatedData, pending: pageLoading } = await useFetch('/api/posts', {
  query: {
    page: currentPage,
    limit: pageSize
  },
  key: 'posts-list',
  transform: (data: any) => {
    return {
      items: data.posts || [],
      total: data.total || 0,
      hasMore: data.hasMore || false
    }
  }
})

// 12. 表单提交
const formData = reactive({
  name: '',
  email: '',
  message: ''
})

const { pending: submitting, execute: submitForm } = await useFetch('/api/contact', {
  method: 'POST',
  body: formData,
  immediate: false,
  onResponse({ response }) {
    if (response.ok) {
      // 重置表单
      Object.assign(formData, { name: '', email: '', message: '' })
    }
  }
})

// ==================== 工具方法 ====================

const toggleShouldFetch = () => {
  shouldFetch.value = !shouldFetch.value
}

const changeUser = (id: number) => {
  selectedUserId.value = id
}

const nextPage = () => {
  if (paginatedData.value?.hasMore) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const handleSubmit = async () => {
  if (formData.name && formData.email && formData.message) {
    await submitForm()
  }
}

// ==================== 监听器 ====================

watch(searchQuery, (newQuery) => {
  console.log('Search query changed:', newQuery)
})

// ==================== 生命周期 ====================

onMounted(() => {
  console.log('Component mounted, data fetched')
})
</script>

<template>
  <div class="fetch-guide-container">
    <header class="guide-header">
      <h1>🚀 Nuxt 3 useFetch 完整指南</h1>
      <p class="subtitle">掌握 Nuxt 3 中所有的数据获取方法</p>
    </header>

    <!-- 基础用法 -->
    <section class="guide-section">
      <h2>📚 基础用法</h2>
      
      <div class="example-grid">
        <div class="example-card">
          <h3>1. 基础 useFetch</h3>
          <div class="code-block">
            <pre><code>const { data, pending, error } = await useFetch('/api/basic')</code></pre>
          </div>
          <div class="result">
            <p><strong>状态:</strong> {{ basicPending ? '加载中...' : '完成' }}</p>
            <p><strong>数据:</strong> {{ basicData || '暂无数据' }}</p>
            <p v-if="basicError" class="error"><strong>错误:</strong> {{ basicError }}</p>
          </div>
        </div>

        <div class="example-card">
          <h3>2. 带参数请求</h3>
          <div class="code-block">
            <pre><code>const { data } = await useFetch(`/api/users/${userId}`)</code></pre>
          </div>
          <div class="controls">
            <button @click="changeUser(1)">用户 1</button>
            <button @click="changeUser(2)">用户 2</button>
            <button @click="refreshUser()">刷新</button>
          </div>
          <div class="result">
            <p><strong>状态:</strong> {{ userPending ? '加载中...' : '完成' }}</p>
            <p><strong>用户:</strong> {{ userData || '暂无数据' }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 响应式参数 -->
    <section class="guide-section">
      <h2>🔄 响应式参数</h2>
      
      <div class="example-card">
        <h3>3. 搜索示例</h3>
        <div class="code-block">
          <pre><code>const { data } = await useFetch('/api/search', {
  query: { q: searchQuery },
  watch: [searchQuery]
})</code></pre>
        </div>
        <div class="controls">
          <input 
            v-model="searchQuery" 
            placeholder="输入搜索关键词..."
            class="search-input"
          >
        </div>
        <div class="result">
          <p><strong>搜索词:</strong> {{ searchQuery || '无' }}</p>
          <p><strong>状态:</strong> {{ searchPending ? '搜索中...' : '完成' }}</p>
          <p><strong>结果:</strong> {{ searchResults || '暂无结果' }}</p>
        </div>
      </div>
    </section>

    <!-- POST 请求 -->
    <section class="guide-section">
      <h2>📝 POST 请求和表单</h2>
      
      <div class="example-card">
        <h3>4. 表单提交</h3>
        <div class="code-block">
          <pre><code>const { execute } = await useFetch('/api/contact', {
  method: 'POST',
  body: formData,
  immediate: false
})</code></pre>
        </div>
        <form @submit.prevent="handleSubmit" class="demo-form">
          <div class="form-group">
            <label>姓名:</label>
            <input v-model="formData.name" type="text" required>
          </div>
          <div class="form-group">
            <label>邮箱:</label>
            <input v-model="formData.email" type="email" required>
          </div>
          <div class="form-group">
            <label>消息:</label>
            <textarea v-model="formData.message" required></textarea>
          </div>
          <button type="submit" :disabled="submitting">
            {{ submitting ? '提交中...' : '提交' }}
          </button>
        </form>
      </div>
    </section>

    <!-- 懒加载 -->
    <section class="guide-section">
      <h2>⏳ 懒加载数据</h2>
      
      <div class="example-grid">
        <div class="example-card">
          <h3>5. useLazyFetch</h3>
          <div class="code-block">
            <pre><code>const { data, pending } = await useLazyFetch('/api/lazy')</code></pre>
          </div>
          <div class="result">
            <p><strong>状态:</strong> {{ lazyPending ? '加载中...' : '完成' }}</p>
            <p><strong>数据:</strong> {{ lazyData || '暂无数据' }}</p>
          </div>
        </div>

        <div class="example-card">
          <h3>6. 条件加载</h3>
          <div class="code-block">
            <pre><code>const { data } = await useFetch('/api/conditional', {
  immediate: shouldFetch.value
})</code></pre>
          </div>
          <div class="controls">
            <button @click="toggleShouldFetch">
              {{ shouldFetch ? '停止获取' : '开始获取' }}
            </button>
          </div>
          <div class="result">
            <p><strong>获取状态:</strong> {{ shouldFetch ? '启用' : '禁用' }}</p>
            <p><strong>数据:</strong> {{ conditionalData || '暂无数据' }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 手动 fetch -->
    <section class="guide-section">
      <h2>🔧 手动 $fetch</h2>
      
      <div class="example-card">
        <h3>7. 手动控制请求</h3>
        <div class="code-block">
          <pre><code>const data = await $fetch('/api/manual')</code></pre>
        </div>
        <div class="controls">
          <button @click="handleManualFetch" :disabled="manualFetching">
            {{ manualFetching ? '获取中...' : '手动获取数据' }}
          </button>
        </div>
        <div class="result">
          <p><strong>状态:</strong> {{ manualFetching ? '获取中...' : '完成' }}</p>
          <p><strong>数据:</strong> {{ manualFetchData || '暂无数据' }}</p>
        </div>
      </div>
    </section>

    <!-- 分页示例 -->
    <section class="guide-section">
      <h2>📄 分页数据</h2>
      
      <div class="example-card">
        <h3>8. 分页列表</h3>
        <div class="code-block">
          <pre><code>const { data } = await useFetch('/api/posts', {
  query: { page: currentPage, limit: pageSize }
})</code></pre>
        </div>
        <div class="pagination-controls">
          <button @click="prevPage" :disabled="currentPage <= 1">上一页</button>
          <span>第 {{ currentPage }} 页</span>
          <button @click="nextPage" :disabled="!paginatedData?.hasMore">下一页</button>
        </div>
        <div class="result">
          <p><strong>状态:</strong> {{ pageLoading ? '加载中...' : '完成' }}</p>
          <p><strong>总数:</strong> {{ paginatedData?.total || 0 }}</p>
          <p><strong>当前页项目:</strong> {{ paginatedData?.items?.length || 0 }}</p>
        </div>
      </div>
    </section>

    <!-- 错误处理 -->
    <section class="guide-section">
      <h2>⚠️ 错误处理</h2>
      
      <div class="example-card">
        <h3>9. 错误处理示例</h3>
        <div class="code-block">
          <pre><code>const { data, error, status } = await useFetch('/api/might-fail', {
  onResponseError({ response }) {
    console.log('Response error:', response.status)
  }
})</code></pre>
        </div>
        <div class="result">
          <p><strong>状态码:</strong> {{ status || '未知' }}</p>
          <p v-if="fetchError" class="error"><strong>错误:</strong> {{ fetchError }}</p>
          <p v-else><strong>数据:</strong> {{ errorData || '暂无数据' }}</p>
        </div>
      </div>
    </section>

    <!-- API 对比 -->
    <section class="guide-section">
      <h2>🔍 API 对比</h2>
      
      <div class="comparison-table">
        <table>
          <thead>
            <tr>
              <th>API</th>
              <th>用途</th>
              <th>SSR</th>
              <th>响应式</th>
              <th>缓存</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>useFetch</code></td>
              <td>通用数据获取</td>
              <td>✅</td>
              <td>✅</td>
              <td>✅</td>
            </tr>
            <tr>
              <td><code>useLazyFetch</code></td>
              <td>懒加载数据</td>
              <td>❌</td>
              <td>✅</td>
              <td>✅</td>
            </tr>
            <tr>
              <td><code>$fetch</code></td>
              <td>手动请求</td>
              <td>✅</td>
              <td>❌</td>
              <td>❌</td>
            </tr>
            <tr>
              <td><code>useAsyncData</code></td>
              <td>自定义异步数据</td>
              <td>✅</td>
              <td>✅</td>
              <td>✅</td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>

    <!-- 导航 -->
    <div class="navigation">
      <NuxtLink to="/use-state">← 返回学习中心</NuxtLink>
      <NuxtLink to="/use-state/docs">📚 useState 文档</NuxtLink>
    </div>
  </div>
</template>

<style lang="less" scoped>
.fetch-guide-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.guide-header {
  text-align: center;
  margin-bottom: 50px;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;

  h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
  }

  .subtitle {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
  }
}

.guide-section {
  margin-bottom: 50px;

  h2 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
    font-size: 1.8em;
  }
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.example-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
  }

  h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
  }
}

.code-block {
  background: #2c3e50;
  border-radius: 8px;
  margin: 15px 0;
  overflow-x: auto;

  pre {
    margin: 0;
    padding: 20px;
    color: #ecf0f1;

    code {
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

.controls {
  margin: 15px 0;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;

  button {
    padding: 8px 16px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;

    &:hover:not(:disabled) {
      background: #2980b9;
      transform: translateY(-1px);
    }

    &:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
    }
  }
}

.search-input {
  width: 100%;
  padding: 10px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #3498db;
  }
}

.result {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-top: 15px;

  p {
    margin: 8px 0;

    &.error {
      color: #e74c3c;
      font-weight: 500;
    }
  }
}

.demo-form {
  .form-group {
    margin: 15px 0;

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #2c3e50;
    }

    input, textarea {
      width: 100%;
      padding: 10px;
      border: 2px solid #e1e5e9;
      border-radius: 6px;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: #3498db;
      }
    }

    textarea {
      height: 80px;
      resize: vertical;
    }
  }

  button[type="submit"] {
    width: 100%;
    padding: 12px;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;

    &:hover:not(:disabled) {
      background: #229954;
    }

    &:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
    }
  }
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 15px 0;

  span {
    font-weight: 500;
    color: #2c3e50;
  }
}

.comparison-table {
  overflow-x: auto;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-radius: 8px;

  table {
    width: 100%;
    border-collapse: collapse;
    background: white;

    th, td {
      padding: 15px;
      text-align: left;
      border-bottom: 1px solid #e1e5e9;
    }

    th {
      background: #f8f9fa;
      font-weight: 600;
      color: #2c3e50;
    }

    code {
      background: #f8f9fa;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Monaco', 'Consolas', monospace;
      color: #e74c3c;
    }

    td:nth-child(3), td:nth-child(4), td:nth-child(5) {
      text-align: center;
      font-size: 1.2em;
    }
  }
}

.navigation {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin: 50px 0;
  flex-wrap: wrap;

  a {
    padding: 12px 24px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s;

    &:hover {
      background: #2980b9;
      transform: translateY(-2px);
    }
  }
}
</style>

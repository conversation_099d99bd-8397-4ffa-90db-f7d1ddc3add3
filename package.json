{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.4.1", "dotenv": "^16.5.0", "eslint": "^9.0.0", "nuxt": "^3.17.5", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "devDependencies": {"less": "^4.3.0", "postcss-custom-media": "^11.0.6", "postcss-nested": "^7.0.2"}}
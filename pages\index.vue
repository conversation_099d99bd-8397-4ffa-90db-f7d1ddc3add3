<script setup>
import '~/assets/css/first.css'

// useHead({
// 	title: "this's abouts",
// 	titleTemplate: (titleChunk) => {
// 		console.log('[30m [ titleChunk ]-5 [0m', titleChunk)
// 		return titleChunk ? `${titleChunk} %separator %name ` : "Rex's Nuxt"
// 	},
// 	templateParams: {
// 		name: '站点标题',
// 		separator: '-',
// 	},
// })

const someErrorLogger= (error)=>{

  console.log('%c [ someErrorLogger ]-18', 'font-size:13px; background:#eefea4; color:#ffffe8;',error );
}
</script>

<template>
	<!-- <div>
		<h1>欢迎来到首页</h1>
		<ButtonText> 这是一个自动导入的组件 </ButtonText>
		<nuxt-link to="/abouts"> 跳转到关于页面 </nuxt-link>
	</div> -->
	<!-- 一些内容 -->
	<NuxtErrorBoundary @error="someErrorLogger">
		<!-- 使用默认插槽渲染你的内容 -->
		<template #error="{ error, clearError }">
			你可以在这里本地显示错误：{{ error }}
			<button @click="clearError">这将清除错误。</button>
		</template>
	</NuxtErrorBoundary>
</template>

<style lang="less" scoped>
@import url('~/assets/css/second.css');

div {
	h1 {
		background-color: palevioletred;
	}

	.text-btn {
		height: 100px;
		cursor: pointer;
	}
}
</style>
